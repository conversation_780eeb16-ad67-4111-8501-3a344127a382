2025-08-26 11:48:57.728589: 61
2025-08-26 11:48:57.836586: 复制 动态库完毕
2025-08-26 11:48:57.840587: ✅ 数据库更新成功: planConfig
2025-08-26 11:48:57.840587: ✅ 数据库更新成功: SysConfig
2025-08-26 11:48:57.840587: ✅ 数据库更新成功: Sip2Config
2025-08-26 11:48:57.841586: ✅ 数据库更新成功: pageConfig
2025-08-26 11:48:57.841586: ✅ 数据库更新成功: readerConfig
2025-08-26 11:48:57.841586: ✅ 数据库更新成功: banZhengConfig
2025-08-26 11:48:57.841586: ✅ 数据库更新成功: printConfig
2025-08-26 11:48:58.043583: 开始初始化闸机协调器...
2025-08-26 11:48:58.043583: ✅ 已清除串口配置缓存，下次访问将重新从数据库读取
2025-08-26 11:48:58.044583: ✅ 已清除 SettingProvider 串口配置缓存
2025-08-26 11:48:58.067583: ✅ 通过 getSerialConfig 获取串口配置: COM1 @ 115200
2025-08-26 11:48:58.067583: ✅ 串口配置加载完成: COM1 @ 115200
2025-08-26 11:48:58.067583: 可用串口: [COM1, COM2, COM3, COM4, COM5, COM6]
2025-08-26 11:48:58.072584: 连接闸机串口: COM1
2025-08-26 11:48:58.072584: 尝试连接串口: COM1, 波特率: 115200
2025-08-26 11:48:58.073583: 串口连接成功: COM1 at 115200 baud
2025-08-26 11:48:58.073583: 开始监听串口数据
2025-08-26 11:48:58.073583: 串口连接状态变化: true
2025-08-26 11:48:58.073583: 闸机串口连接成功
2025-08-26 11:48:58.073583: 串口 COM1 连接成功 (波特率: 115200)
2025-08-26 11:48:58.073583: 闸机串口服务初始化成功: COM1
2025-08-26 11:48:58.073583: 开始监听串口数据（通过 GateSerialManager 事件流）
2025-08-26 11:48:58.074583: 开始监听闸机串口命令
2025-08-26 11:48:58.074583: 开始初始化RFID服务和共享池...
2025-08-26 11:48:58.074583: 开始初始化增强RFID服务...
2025-08-26 11:48:58.074583: 开始初始化增强RFID服务...
2025-08-26 11:48:58.075598: SIP2图书信息服务初始化完成
2025-08-26 11:48:58.075598: 增强RFID服务初始化完成，配置了1个阅读器
2025-08-26 11:48:58.077583: 📋 从数据库读取主从机配置: channel_1
2025-08-26 11:48:58.078584: 📋 配置详情: 主机模式
2025-08-26 11:48:58.078584: 🚀 主机模式：启动RFID硬件持续扫描
2025-08-26 11:48:58.078584: 启动RFID持续扫描...
2025-08-26 11:48:58.078584: changeReaders
2025-08-26 11:48:58.078584: createIsolate isOpen:false,isOpening:false
2025-08-26 11:48:58.080582: createIsolate newport null
2025-08-26 11:48:58.088589: socket 连接成功,isBroadcast:false
2025-08-26 11:48:58.088589: changeSocketStatus:true
2025-08-26 11:48:58.088589: Sip2HeartBeatManager start loginACS:false askACS:false
2025-08-26 11:48:58.089583: Req msgType：Sip2MsgType.login ,length:72， ret:  9300CNhlsp_sip2|COsip2|CP3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AY1AZEC16
2025-08-26 11:48:58.190580: Rsp : 941AY1AZFDFC
2025-08-26 11:48:58.201582: loginRsp:{OK: 1, MsgSeqId: 1AZFDFC}
2025-08-26 11:48:58.201582: Sip2HeartBeatManager start loginACS:false askACS:true
2025-08-26 11:48:58.201582: 发送心跳
2025-08-26 11:48:58.201582: Req msgType：Sip2MsgType.scStatus ,length:20， ret:  9900522.00AY2AZFC9F
2025-08-26 11:48:58.360578: Rsp : 98YYYNNN00500320250826    1148512.00AOhlsp|AM海恒图书馆|BXYYYYYYYYYYYYYYYY|AN3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AF|AG|AY2AZD51D
2025-08-26 11:48:58.582574: 找到网口配置: LSGate图书馆安全门RFID阅读器 - **************:6012
2025-08-26 11:48:58.582574: 使用网口连接: **************:6012
2025-08-26 11:48:58.583574: open():SendPort
2025-08-26 11:48:58.583574: untilDetcted():SendPort
2025-08-26 11:48:58.583574: 网口连接成功: **************:6012
2025-08-26 11:48:58.583574: startInventory():SendPort
2025-08-26 11:48:58.584573: RFID硬件扫描已启动，阅读器开始持续工作
2025-08-26 11:48:58.584573: RFID持续扫描启动完成
2025-08-26 11:48:58.584573: 增强RFID服务初始化完成，持续扫描已启动
2025-08-26 11:48:58.584573: 📋 从数据库读取主从机配置: channel_1
2025-08-26 11:48:58.584573: 📋 配置详情: 主机模式
2025-08-26 11:48:58.585573: 🚀 主机模式：启动持续数据收集，数据将持续进入共享池
2025-08-26 11:48:58.585573: 🎯 关键修复：使用轮询机制确保标签持续被发现
2025-08-26 11:48:58.585573: 🧹 清空RFID缓冲区（保持tagList），确保数据收集正常工作...
2025-08-26 11:48:58.585573: 清空RFID扫描缓冲区...
2025-08-26 11:48:58.585573: 🧹 已清空HWTagProvider: 0 -> 0个标签
2025-08-26 11:48:58.585573: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-26 11:48:58.585573: 🔄 开始重置已处理条码集合...
2025-08-26 11:48:58.585573: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 11:48:58.585573: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 11:48:58.586573: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 11:48:58.586573: 📊 当前tagList状态: 0个标签
2025-08-26 11:48:58.586573: ⚠️ 当前未在扫描状态，跳过立即轮询
2025-08-26 11:48:58.586573: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-26 11:48:58.586573: 🚀 启动增强数据收集（事件监听 + 轮询备用）...
2025-08-26 11:48:58.586573: 🚀 开始RFID数据收集...
2025-08-26 11:48:58.587574: 📋 扫描结果和缓存已清空
2025-08-26 11:48:58.587574: 清空RFID扫描缓冲区...
2025-08-26 11:48:58.587574: 🧹 已清空HWTagProvider: 0 -> 0个标签
2025-08-26 11:48:58.587574: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-26 11:48:58.587574: 🔄 开始重置已处理条码集合...
2025-08-26 11:48:58.587574: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 11:48:58.587574: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 11:48:58.588573: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 11:48:58.588573: 📊 当前tagList状态: 0个标签
2025-08-26 11:48:58.588573: ⚠️ 当前未在扫描状态，跳过立即轮询
2025-08-26 11:48:58.588573: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-26 11:48:58.588573: 🎯 启动数据监听和轮询机制...
2025-08-26 11:48:58.588573: 🚀 标签轮询机制已启动 (每500ms轮询一次)
2025-08-26 11:48:58.589573: ✅ 标签监听改为仅轮询机制（每500ms轮询tagList）
2025-08-26 11:48:58.589573: 📊 当前HWTagProvider状态:
2025-08-26 11:48:58.589573:   - tagList: 0个标签
2025-08-26 11:48:58.589573:   - type: HWTagType.tagList
2025-08-26 11:48:58.589573: 🎯 标签数据获取已统一为轮询机制
2025-08-26 11:48:58.589573: ✅ RFID数据收集已启动，轮询机制运行中
2025-08-26 11:48:58.589573: 📊 当前tagList状态: 0个标签
2025-08-26 11:48:58.589573: subThread :ReaderCommand.readerList
2025-08-26 11:48:58.590574: commandRsp:ReaderCommand.readerList
2025-08-26 11:48:58.590574: readerList：1,readerSetting：1
2025-08-26 11:48:58.590574: cacheUsedReaders:1
2025-08-26 11:48:58.590574: subThread :ReaderCommand.open
2025-08-26 11:48:58.590574: commandRsp:ReaderCommand.open
2025-08-26 11:48:58.591574: LSGate使用网络连接 IP: **************, Port: 6012, DeviceType: LSGControlCenter
2025-08-26 11:48:58.604575: LSGate device opened successfully, handle: 2395416852128
2025-08-26 11:48:58.606578: open reader readerType ：22 ret：0
2025-08-26 11:48:58.606578: [[22, 0]]
2025-08-26 11:48:58.607576: changeType:ReaderErrorType.openSuccess
2025-08-26 11:48:58.611578: subThread :ReaderCommand.untilDetected
2025-08-26 11:48:58.611578: commandRsp:ReaderCommand.untilDetected
2025-08-26 11:48:58.612574: subThread :ReaderCommand.startInventory
2025-08-26 11:48:58.612574: commandRsp:ReaderCommand.startInventory
2025-08-26 11:48:58.690578: 🔄 执行首次轮询...
2025-08-26 11:48:58.691575: 🔄 开始RFID轮询检查...
2025-08-26 11:48:58.692573: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:48:58.692573: ⚠️ tagList为空，场上无标签
2025-08-26 11:48:59.090565: 🔄 开始RFID轮询检查...
2025-08-26 11:48:59.090565: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:48:59.090565: ⚠️ tagList为空，场上无标签
2025-08-26 11:48:59.212564: 🔍 LSGate硬件扫描详情:
2025-08-26 11:48:59.213563:   - 设备句柄: 2395416852128
2025-08-26 11:48:59.213563:   - FetchRecords返回值: 0
2025-08-26 11:48:59.213563:   - 报告数量: 0
2025-08-26 11:48:59.213563: 📊 LSGate扫描结果汇总:
2025-08-26 11:48:59.213563:   - 发现标签数量: 0
2025-08-26 11:48:59.214563:   - 未发现任何RFID标签
2025-08-26 11:48:59.214563: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:48:59.214563: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:48:59.590556: 🔍 验证数据收集状态...
2025-08-26 11:48:59.590556: 📊 当前tagList: 0个标签
2025-08-26 11:48:59.591556: ⚠️ tagList为空，等待RFID硬件扫描到标签
2025-08-26 11:48:59.591556: ✅ 主机持续数据收集已启动，共享池将持续接收RFID数据
2025-08-26 11:48:59.591556: 🔄 轮询机制每500ms检查一次tagList，确保标签不会丢失
2025-08-26 11:48:59.591556: 开始全局初始化共享扫描池服务...
2025-08-26 11:48:59.591556: 共享扫描池已集成现有RFID服务
2025-08-26 11:48:59.591556: 📡 初始化后RFID扫描状态: scanning=false
2025-08-26 11:48:59.592556: 🔄 开始重置已处理条码集合...
2025-08-26 11:48:59.592556: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 11:48:59.592556: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 11:48:59.592556: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 11:48:59.592556: 📊 当前tagList状态: 0个标签
2025-08-26 11:48:59.592556: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 11:48:59.592556: 🔄 开始RFID轮询检查...
2025-08-26 11:48:59.592556: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:48:59.593557: ⚠️ tagList为空，场上无标签
2025-08-26 11:48:59.593557: 🔄 开始RFID轮询检查...
2025-08-26 11:48:59.593557: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:48:59.593557: ⚠️ tagList为空，场上无标签
2025-08-26 11:48:59.593557: 共享扫描池服务全局初始化完成
2025-08-26 11:48:59.593557: 🚀 初始化新架构服务...
2025-08-26 11:48:59.593557: 🖥️ 主机模式：使用主机集合A服务
2025-08-26 11:48:59.594556: ⏹️ 书籍信息查询服务停止监听
2025-08-26 11:48:59.594556: 🚀 书籍信息查询服务开始监听集合A变化
2025-08-26 11:48:59.594556: ✅ 新架构服务初始化完成
2025-08-26 11:48:59.594556: RFID服务和共享池初始化完成，持续扫描已启动
2025-08-26 11:48:59.594556: 闸机协调器初始化完成
2025-08-26 11:48:59.594556: 🔧 开始初始化主从机扩展（使用持久化配置）...
2025-08-26 11:48:59.594556: 开始初始化主从机扩展...
2025-08-26 11:48:59.595556: 从 seasetting 数据库加载主从机配置成功: channel_1
2025-08-26 11:48:59.595556: 配置详情: 主机模式
2025-08-26 11:48:59.596558: 📡 从 SettingProvider 获取串口配置: COM1 @ 115200
2025-08-26 11:48:59.597556: ✅ 通过 SettingProvider 加载串口配置成功
2025-08-26 11:48:59.597556: 启用主从机扩展: channel_1 (主机)
2025-08-26 11:48:59.597556: ✅ 数据变化通知流已创建
2025-08-26 11:48:59.597556: 共享扫描池已集成现有RFID服务
2025-08-26 11:48:59.597556: 📡 初始化后RFID扫描状态: scanning=false
2025-08-26 11:48:59.597556: 🔄 开始重置已处理条码集合...
2025-08-26 11:48:59.598556: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 11:48:59.598556: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 11:48:59.598556: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 11:48:59.598556: 📊 当前tagList状态: 0个标签
2025-08-26 11:48:59.598556: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 11:48:59.598556: 🔄 开始RFID轮询检查...
2025-08-26 11:48:59.598556: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:48:59.598556: ⚠️ tagList为空，场上无标签
2025-08-26 11:48:59.599556: 配置为主机模式，监听端口: 8888
2025-08-26 11:48:59.600556: 主机服务器启动成功，监听端口: 8888
2025-08-26 11:48:59.600556: 主机模式配置完成（请求-响应模式）
2025-08-26 11:48:59.600556: [channel_1] 已集成现有GateCoordinator，开始监听事件
2025-08-26 11:48:59.600556: 主从机扩展启用成功
2025-08-26 11:48:59.600556: 主从机扩展初始化完成
2025-08-26 11:48:59.600556: 配置信息: MasterSlaveConfig(channelId: channel_1, isMaster: true, slaveAddress: null, masterAddress: null, port: 8888)
2025-08-26 11:48:59.600556: ✅ 加载到持久化配置: 主机模式, 通道: channel_1
2025-08-26 11:48:59.601556: 主从机扩展初始化完成
2025-08-26 11:48:59.601556: 安全闸机系统初始化完成
2025-08-26 11:48:59.610555: 开始初始化MultiAuthManager...
2025-08-26 11:48:59.610555: 多认证管理器状态变更: initializing
2025-08-26 11:48:59.611556: 认证优先级管理器: 开始加载认证方式
2025-08-26 11:48:59.611556: 配置的排序: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝二维码认证, 芝麻信用码认证, 支付宝二维码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-08-26 11:48:59.611556: 可用的认证方式: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝二维码认证, 芝麻信用码认证, 支付宝二维码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-08-26 11:48:59.612556: 认证优先级管理器: 按配置顺序添加 读者证认证 -> 读者证
2025-08-26 11:48:59.612556: 认证优先级管理器: 最终排序结果: 读者证
2025-08-26 11:48:59.612556: 认证优先级管理器: 主要认证方式: 读者证
2025-08-26 11:48:59.612556: 多认证管理器: 从优先级管理器加载的认证方式: 读者证
2025-08-26 11:48:59.612556: 多认证管理器: 当前默认显示方式: 读者证
2025-08-26 11:48:59.612556: 初始化读卡器认证服务
2025-08-26 11:48:59.612556: 读卡器认证服务初始化成功
2025-08-26 11:48:59.613557: 初始化共享读卡器认证服务
2025-08-26 11:48:59.613557: 读者证 认证服务初始化成功
2025-08-26 11:48:59.613557: 认证服务初始化完成，共初始化 1 种认证方式
2025-08-26 11:48:59.613557: 多认证管理器状态变更: idle
2025-08-26 11:48:59.614556: 多认证管理器初始化完成，启用的认证方式: [AuthMethod.readerCard]
2025-08-26 11:48:59.614556: MultiAuthManager初始化完成
2025-08-26 11:48:59.614556: 开始初始化SilencePageViewModel...
2025-08-26 11:48:59.614556: 闸机串口服务已经初始化
2025-08-26 11:48:59.614556: 开始初始化闸机认证服务...
2025-08-26 11:48:59.614556: 闸机认证服务初始化完成，启用认证方式: 人脸识别、读者证、AuthMethod.wechatScanQRCode
2025-08-26 11:48:59.615556: RFID服务已经初始化
2025-08-26 11:48:59.615556: SIP2图书信息服务初始化完成
2025-08-26 11:48:59.615556: 💡 主从机扩展已准备就绪，请通过配置页面手动启用
2025-08-26 11:48:59.615556: 💡 可以通过MasterSlaveConfigPage进行配置
2025-08-26 11:48:59.615556: ✅ 统一事件监听已设置：SilencePageViewModel → GateCoordinator.eventStream
2025-08-26 11:48:59.616556: 串口监听已经启动
2025-08-26 11:48:59.616556: SilencePageViewModel初始化完成
2025-08-26 11:48:59.617558: 🔍 LSGate硬件扫描详情:
2025-08-26 11:48:59.618556:   - 设备句柄: 2395416852128
2025-08-26 11:48:59.618556:   - FetchRecords返回值: 0
2025-08-26 11:48:59.618556:   - 报告数量: 0
2025-08-26 11:48:59.618556: 📊 LSGate扫描结果汇总:
2025-08-26 11:48:59.618556:   - 发现标签数量: 0
2025-08-26 11:48:59.619555:   - 未发现任何RFID标签
2025-08-26 11:48:59.619555: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:48:59.619555: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:48:59.959550: dispose IndexPage
2025-08-26 11:48:59.960550: IndexPage dispose
2025-08-26 11:49:00.090549: 🔄 开始RFID轮询检查...
2025-08-26 11:49:00.090549: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:00.091549: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:00.108547: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:00.109548:   - 设备句柄: 2395416852128
2025-08-26 11:49:00.109548:   - FetchRecords返回值: 0
2025-08-26 11:49:00.109548:   - 报告数量: 0
2025-08-26 11:49:00.109548: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:00.110553:   - 发现标签数量: 0
2025-08-26 11:49:00.110553:   - 未发现任何RFID标签
2025-08-26 11:49:00.110553: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:00.110553: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:00.117547: 🔍 主从机模式检测: 启用=true, 主机模式=true
2025-08-26 11:49:00.117547: 🔍 扩展详细状态: {enabled: true, channel_id: channel_1, is_master: true, data_stream_ready: true, data_stream_exists: true, data_stream_closed: false, shared_pool_size: 0, queue_size: 0, comm_connected: false, timestamp: 2025-08-26T11:49:00.117547}
2025-08-26 11:49:00.117547: 🎯 检测到主机模式，无需设置数据监听
2025-08-26 11:49:00.590538: 🔄 开始RFID轮询检查...
2025-08-26 11:49:00.590538: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:00.590538: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:00.608542: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:00.608542:   - 设备句柄: 2395416852128
2025-08-26 11:49:00.608542:   - FetchRecords返回值: 0
2025-08-26 11:49:00.609539:   - 报告数量: 0
2025-08-26 11:49:00.609539: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:00.609539:   - 发现标签数量: 0
2025-08-26 11:49:00.609539:   - 未发现任何RFID标签
2025-08-26 11:49:00.610546: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:00.610546: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:01.090530: 🔄 开始RFID轮询检查...
2025-08-26 11:49:01.090530: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:01.090530: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:01.107530: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:01.108530:   - 设备句柄: 2395416852128
2025-08-26 11:49:01.108530:   - FetchRecords返回值: 0
2025-08-26 11:49:01.108530:   - 报告数量: 0
2025-08-26 11:49:01.108530: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:01.109531:   - 发现标签数量: 0
2025-08-26 11:49:01.109531:   - 未发现任何RFID标签
2025-08-26 11:49:01.109531: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:01.109531: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:01.590523: 🔄 开始RFID轮询检查...
2025-08-26 11:49:01.590523: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:01.590523: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:01.608521: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:01.609521:   - 设备句柄: 2395416852128
2025-08-26 11:49:01.609521:   - FetchRecords返回值: 0
2025-08-26 11:49:01.609521:   - 报告数量: 0
2025-08-26 11:49:01.609521: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:01.610521:   - 发现标签数量: 0
2025-08-26 11:49:01.610521:   - 未发现任何RFID标签
2025-08-26 11:49:01.610521: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:01.610521: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:02.091512: 🔄 开始RFID轮询检查...
2025-08-26 11:49:02.091512: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:02.091512: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:02.107512: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:02.107512:   - 设备句柄: 2395416852128
2025-08-26 11:49:02.107512:   - FetchRecords返回值: 0
2025-08-26 11:49:02.108512:   - 报告数量: 0
2025-08-26 11:49:02.108512: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:02.108512:   - 发现标签数量: 0
2025-08-26 11:49:02.108512:   - 未发现任何RFID标签
2025-08-26 11:49:02.108512: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:02.108512: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:02.590504: 🔄 开始RFID轮询检查...
2025-08-26 11:49:02.590504: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:02.591505: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:02.607505: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:02.608505:   - 设备句柄: 2395416852128
2025-08-26 11:49:02.609508:   - FetchRecords返回值: 0
2025-08-26 11:49:02.609508:   - 报告数量: 0
2025-08-26 11:49:02.610504: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:02.610504:   - 发现标签数量: 0
2025-08-26 11:49:02.610504:   - 未发现任何RFID标签
2025-08-26 11:49:02.611504: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:02.611504: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:02.921498: 时钟连续点击5次，进入验证页面
2025-08-26 11:49:03.090495: 🔄 开始RFID轮询检查...
2025-08-26 11:49:03.090495: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:03.091496: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:03.107495: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:03.108496:   - 设备句柄: 2395416852128
2025-08-26 11:49:03.108496:   - FetchRecords返回值: 0
2025-08-26 11:49:03.108496:   - 报告数量: 0
2025-08-26 11:49:03.108496: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:03.109497:   - 发现标签数量: 0
2025-08-26 11:49:03.109497:   - 未发现任何RFID标签
2025-08-26 11:49:03.109497: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:03.109497: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:03.590486: 🔄 开始RFID轮询检查...
2025-08-26 11:49:03.590486: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:03.590486: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:03.608486: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:03.608486:   - 设备句柄: 2395416852128
2025-08-26 11:49:03.608486:   - FetchRecords返回值: 0
2025-08-26 11:49:03.608486:   - 报告数量: 0
2025-08-26 11:49:03.609487: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:03.609487:   - 发现标签数量: 0
2025-08-26 11:49:03.609487:   - 未发现任何RFID标签
2025-08-26 11:49:03.609487: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:03.609487: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:04.115481: 📖 读取表单字段: device_register.device_mac = FF-FF-FF-FF-FF-FF
2025-08-26 11:49:04.115481: 📖 反显字段值: device_register.device_mac = FF-FF-FF-FF-FF-FF
2025-08-26 11:49:04.116478: 📖 读取表单字段: device_register.device_name = 
2025-08-26 11:49:04.116478: 📖 读取表单字段: device_register.door_code = 
2025-08-26 11:49:04.116478: 📖 读取表单字段: device_register.door_name = 
2025-08-26 11:49:04.116478: 📖 读取表单字段: device_register.area_code = 
2025-08-26 11:49:04.116478: 📖 读取表单字段: device_register.area_name = 
2025-08-26 11:49:04.116478: 📖 读取表单字段: device_register.lib_id = 
2025-08-26 11:49:04.116478: 📖 读取表单字段: device_register.lib_name = 
2025-08-26 11:49:04.116478: 📖 读取表单字段: device_register.location = 
2025-08-26 11:49:04.116478: 📖 读取表单字段: device_register.description = 
2025-08-26 11:49:04.117477: 📖 读取表单字段: device_register.ip_address = 
2025-08-26 11:49:04.117477: 📖 读取表单字段: device_register.port = 
2025-08-26 11:49:04.117477: 设备API服务初始化完成: http://************:9000
2025-08-26 11:49:04.117477: 🔄 开始RFID轮询检查...
2025-08-26 11:49:04.117477: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:04.117477: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:04.118477: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:04.118477:   - 设备句柄: 2395416852128
2025-08-26 11:49:04.118477:   - FetchRecords返回值: 0
2025-08-26 11:49:04.118477:   - 报告数量: 0
2025-08-26 11:49:04.118477: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:04.118477:   - 发现标签数量: 0
2025-08-26 11:49:04.118477:   - 未发现任何RFID标签
2025-08-26 11:49:04.118477: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:04.119477: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:04.590469: 🔄 开始RFID轮询检查...
2025-08-26 11:49:04.590469: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:04.590469: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:04.607470: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:04.608471:   - 设备句柄: 2395416852128
2025-08-26 11:49:04.608471:   - FetchRecords返回值: 0
2025-08-26 11:49:04.608471:   - 报告数量: 0
2025-08-26 11:49:04.609469: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:04.609469:   - 发现标签数量: 0
2025-08-26 11:49:04.609469:   - 未发现任何RFID标签
2025-08-26 11:49:04.609469: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:04.609469: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:05.090463: 🔄 开始RFID轮询检查...
2025-08-26 11:49:05.091462: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:05.092460: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:05.108460: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:05.109464:   - 设备句柄: 2395416852128
2025-08-26 11:49:05.109464:   - FetchRecords返回值: 0
2025-08-26 11:49:05.109464:   - 报告数量: 0
2025-08-26 11:49:05.109464: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:05.109464:   - 发现标签数量: 0
2025-08-26 11:49:05.110460:   - 未发现任何RFID标签
2025-08-26 11:49:05.110460: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:05.110460: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:05.591452: 🔄 开始RFID轮询检查...
2025-08-26 11:49:05.591452: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:05.591452: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:05.607452: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:05.608456:   - 设备句柄: 2395416852128
2025-08-26 11:49:05.608456:   - FetchRecords返回值: 0
2025-08-26 11:49:05.609452:   - 报告数量: 0
2025-08-26 11:49:05.609452: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:05.610452:   - 发现标签数量: 0
2025-08-26 11:49:05.610452:   - 未发现任何RFID标签
2025-08-26 11:49:05.610452: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:05.610452: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:06.091443: 🔄 开始RFID轮询检查...
2025-08-26 11:49:06.092443: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:06.092443: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:06.108446: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:06.109449:   - 设备句柄: 2395416852128
2025-08-26 11:49:06.110443:   - FetchRecords返回值: 0
2025-08-26 11:49:06.110443:   - 报告数量: 0
2025-08-26 11:49:06.110443: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:06.110443:   - 发现标签数量: 0
2025-08-26 11:49:06.110443:   - 未发现任何RFID标签
2025-08-26 11:49:06.111443: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:06.111443: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:06.591435: 🔄 开始RFID轮询检查...
2025-08-26 11:49:06.591435: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:06.592435: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:06.608434: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:06.608434:   - 设备句柄: 2395416852128
2025-08-26 11:49:06.608434:   - FetchRecords返回值: 0
2025-08-26 11:49:06.609434:   - 报告数量: 0
2025-08-26 11:49:06.609434: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:06.609434:   - 发现标签数量: 0
2025-08-26 11:49:06.609434:   - 未发现任何RFID标签
2025-08-26 11:49:06.609434: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:06.609434: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:07.090425: 🔄 开始RFID轮询检查...
2025-08-26 11:49:07.090425: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:07.090425: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:07.109428: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:07.109428:   - 设备句柄: 2395416852128
2025-08-26 11:49:07.109428:   - FetchRecords返回值: 0
2025-08-26 11:49:07.109428:   - 报告数量: 0
2025-08-26 11:49:07.110428: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:07.110428:   - 发现标签数量: 0
2025-08-26 11:49:07.110428:   - 未发现任何RFID标签
2025-08-26 11:49:07.110428: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:07.110428: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:07.591417: 🔄 开始RFID轮询检查...
2025-08-26 11:49:07.591417: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:07.592418: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:07.607417: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:07.608417:   - 设备句柄: 2395416852128
2025-08-26 11:49:07.608417:   - FetchRecords返回值: 0
2025-08-26 11:49:07.608417:   - 报告数量: 0
2025-08-26 11:49:07.608417: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:07.608417:   - 发现标签数量: 0
2025-08-26 11:49:07.608417:   - 未发现任何RFID标签
2025-08-26 11:49:07.609417: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:07.609417: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:08.089411: 🔄 开始RFID轮询检查...
2025-08-26 11:49:08.089411: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:08.090413: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:08.108407: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:08.108407:   - 设备句柄: 2395416852128
2025-08-26 11:49:08.108407:   - FetchRecords返回值: 0
2025-08-26 11:49:08.109407:   - 报告数量: 0
2025-08-26 11:49:08.109407: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:08.109407:   - 发现标签数量: 0
2025-08-26 11:49:08.109407:   - 未发现任何RFID标签
2025-08-26 11:49:08.109407: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:08.109407: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:08.591399: 🔄 开始RFID轮询检查...
2025-08-26 11:49:08.591399: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:08.592402: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:08.609407: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:08.609407:   - 设备句柄: 2395416852128
2025-08-26 11:49:08.610403:   - FetchRecords返回值: 0
2025-08-26 11:49:08.610403:   - 报告数量: 0
2025-08-26 11:49:08.610403: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:08.610403:   - 发现标签数量: 0
2025-08-26 11:49:08.611400:   - 未发现任何RFID标签
2025-08-26 11:49:08.611400: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:08.611400: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:09.090390: 🔄 开始RFID轮询检查...
2025-08-26 11:49:09.090390: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:09.090390: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:09.109391: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:09.109391:   - 设备句柄: 2395416852128
2025-08-26 11:49:09.110391:   - FetchRecords返回值: 0
2025-08-26 11:49:09.110391:   - 报告数量: 0
2025-08-26 11:49:09.110391: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:09.110391:   - 发现标签数量: 0
2025-08-26 11:49:09.110391:   - 未发现任何RFID标签
2025-08-26 11:49:09.111393: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:09.111393: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:09.591383: 🔄 开始RFID轮询检查...
2025-08-26 11:49:09.591383: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:09.592384: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:09.608382: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:09.608382:   - 设备句柄: 2395416852128
2025-08-26 11:49:09.609382:   - FetchRecords返回值: 0
2025-08-26 11:49:09.609382:   - 报告数量: 0
2025-08-26 11:49:09.609382: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:09.609382:   - 发现标签数量: 0
2025-08-26 11:49:09.609382:   - 未发现任何RFID标签
2025-08-26 11:49:09.609382: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:09.610382: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:10.090374: 🔄 开始RFID轮询检查...
2025-08-26 11:49:10.090374: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:10.090374: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:10.108375: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:10.108375:   - 设备句柄: 2395416852128
2025-08-26 11:49:10.108375:   - FetchRecords返回值: 0
2025-08-26 11:49:10.109373:   - 报告数量: 0
2025-08-26 11:49:10.109373: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:10.109373:   - 发现标签数量: 0
2025-08-26 11:49:10.109373:   - 未发现任何RFID标签
2025-08-26 11:49:10.109373: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:10.109373: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:10.591367: 🔄 开始RFID轮询检查...
2025-08-26 11:49:10.592371: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:10.592371: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:10.610366: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:10.610366:   - 设备句柄: 2395416852128
2025-08-26 11:49:10.611368:   - FetchRecords返回值: 0
2025-08-26 11:49:10.611368:   - 报告数量: 0
2025-08-26 11:49:10.611368: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:10.611368:   - 发现标签数量: 0
2025-08-26 11:49:10.612365:   - 未发现任何RFID标签
2025-08-26 11:49:10.612365: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:10.613367: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:11.091357: 🔄 开始RFID轮询检查...
2025-08-26 11:49:11.091357: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:11.091357: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:11.109356: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:11.110356:   - 设备句柄: 2395416852128
2025-08-26 11:49:11.110356:   - FetchRecords返回值: 0
2025-08-26 11:49:11.110356:   - 报告数量: 0
2025-08-26 11:49:11.110356: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:11.111356:   - 发现标签数量: 0
2025-08-26 11:49:11.111356:   - 未发现任何RFID标签
2025-08-26 11:49:11.111356: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:11.111356: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:11.591347: 🔄 开始RFID轮询检查...
2025-08-26 11:49:11.591347: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:11.591347: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:11.608347: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:11.608347:   - 设备句柄: 2395416852128
2025-08-26 11:49:11.609347:   - FetchRecords返回值: 0
2025-08-26 11:49:11.609347:   - 报告数量: 0
2025-08-26 11:49:11.609347: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:11.609347:   - 发现标签数量: 0
2025-08-26 11:49:11.609347:   - 未发现任何RFID标签
2025-08-26 11:49:11.609347: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:11.610347: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:12.090338: 🔄 开始RFID轮询检查...
2025-08-26 11:49:12.090338: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:12.090338: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:12.108339: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:12.109342:   - 设备句柄: 2395416852128
2025-08-26 11:49:12.109342:   - FetchRecords返回值: 0
2025-08-26 11:49:12.109342:   - 报告数量: 0
2025-08-26 11:49:12.110339: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:12.110339:   - 发现标签数量: 0
2025-08-26 11:49:12.110339:   - 未发现任何RFID标签
2025-08-26 11:49:12.110339: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:12.111338: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:12.591331: 🔄 开始RFID轮询检查...
2025-08-26 11:49:12.591331: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:12.592331: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:12.608330: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:12.609330:   - 设备句柄: 2395416852128
2025-08-26 11:49:12.609330:   - FetchRecords返回值: 0
2025-08-26 11:49:12.609330:   - 报告数量: 0
2025-08-26 11:49:12.610330: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:12.610330:   - 发现标签数量: 0
2025-08-26 11:49:12.610330:   - 未发现任何RFID标签
2025-08-26 11:49:12.610330: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:12.610330: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:13.091327: 🔄 开始RFID轮询检查...
2025-08-26 11:49:13.092328: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:13.093322: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:13.108321: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:13.108321:   - 设备句柄: 2395416852128
2025-08-26 11:49:13.109323:   - FetchRecords返回值: 0
2025-08-26 11:49:13.109323:   - 报告数量: 0
2025-08-26 11:49:13.109323: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:13.109323:   - 发现标签数量: 0
2025-08-26 11:49:13.109323:   - 未发现任何RFID标签
2025-08-26 11:49:13.110322: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:13.110322: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:13.591312: 🔄 开始RFID轮询检查...
2025-08-26 11:49:13.591312: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:13.591312: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:13.608312: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:13.608312:   - 设备句柄: 2395416852128
2025-08-26 11:49:13.608312:   - FetchRecords返回值: 0
2025-08-26 11:49:13.609312:   - 报告数量: 0
2025-08-26 11:49:13.609312: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:13.609312:   - 发现标签数量: 0
2025-08-26 11:49:13.609312:   - 未发现任何RFID标签
2025-08-26 11:49:13.609312: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:13.609312: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:14.090304: 🔄 开始RFID轮询检查...
2025-08-26 11:49:14.090304: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:14.090304: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:14.108304: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:14.108304:   - 设备句柄: 2395416852128
2025-08-26 11:49:14.109304:   - FetchRecords返回值: 0
2025-08-26 11:49:14.109304:   - 报告数量: 0
2025-08-26 11:49:14.109304: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:14.109304:   - 发现标签数量: 0
2025-08-26 11:49:14.110304:   - 未发现任何RFID标签
2025-08-26 11:49:14.110304: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:14.110304: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:14.591295: 🔄 开始RFID轮询检查...
2025-08-26 11:49:14.591295: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:14.592296: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:14.608297: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:14.609296:   - 设备句柄: 2395416852128
2025-08-26 11:49:14.609296:   - FetchRecords返回值: 0
2025-08-26 11:49:14.610296:   - 报告数量: 0
2025-08-26 11:49:14.610296: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:14.610296:   - 发现标签数量: 0
2025-08-26 11:49:14.611297:   - 未发现任何RFID标签
2025-08-26 11:49:14.611297: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:14.611297: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:15.090290: 🔄 开始RFID轮询检查...
2025-08-26 11:49:15.091288: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:15.091288: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:15.108286: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:15.108286:   - 设备句柄: 2395416852128
2025-08-26 11:49:15.109286:   - FetchRecords返回值: 0
2025-08-26 11:49:15.109286:   - 报告数量: 0
2025-08-26 11:49:15.109286: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:15.109286:   - 发现标签数量: 0
2025-08-26 11:49:15.109286:   - 未发现任何RFID标签
2025-08-26 11:49:15.109286: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:15.109286: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:15.591278: 🔄 开始RFID轮询检查...
2025-08-26 11:49:15.591278: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:15.591278: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:15.608277: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:15.608277:   - 设备句柄: 2395416852128
2025-08-26 11:49:15.608277:   - FetchRecords返回值: 0
2025-08-26 11:49:15.609281:   - 报告数量: 0
2025-08-26 11:49:15.609281: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:15.609281:   - 发现标签数量: 0
2025-08-26 11:49:15.609281:   - 未发现任何RFID标签
2025-08-26 11:49:15.609281: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:15.609281: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:16.091269: 🔄 开始RFID轮询检查...
2025-08-26 11:49:16.091269: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:16.091269: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:16.108270: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:16.109269:   - 设备句柄: 2395416852128
2025-08-26 11:49:16.109269:   - FetchRecords返回值: 0
2025-08-26 11:49:16.110269:   - 报告数量: 0
2025-08-26 11:49:16.110269: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:16.110269:   - 发现标签数量: 0
2025-08-26 11:49:16.110269:   - 未发现任何RFID标签
2025-08-26 11:49:16.111268: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:16.111268: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:16.591260: 🔄 开始RFID轮询检查...
2025-08-26 11:49:16.591260: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:16.592261: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:16.609259: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:16.609259:   - 设备句柄: 2395416852128
2025-08-26 11:49:16.609259:   - FetchRecords返回值: 0
2025-08-26 11:49:16.610260:   - 报告数量: 0
2025-08-26 11:49:16.610260: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:16.610260:   - 发现标签数量: 0
2025-08-26 11:49:16.610260:   - 未发现任何RFID标签
2025-08-26 11:49:16.610260: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:16.610260: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:17.091251: 🔄 开始RFID轮询检查...
2025-08-26 11:49:17.091251: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:17.091251: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:17.108252: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:17.109255:   - 设备句柄: 2395416852128
2025-08-26 11:49:17.110252:   - FetchRecords返回值: 0
2025-08-26 11:49:17.111253:   - 报告数量: 0
2025-08-26 11:49:17.111253: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:17.111253:   - 发现标签数量: 0
2025-08-26 11:49:17.112252:   - 未发现任何RFID标签
2025-08-26 11:49:17.112252: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:17.112252: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:17.591243: 🔄 开始RFID轮询检查...
2025-08-26 11:49:17.592243: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:17.592243: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:17.608244: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:17.609243:   - 设备句柄: 2395416852128
2025-08-26 11:49:17.610244:   - FetchRecords返回值: 0
2025-08-26 11:49:17.610244:   - 报告数量: 0
2025-08-26 11:49:17.610244: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:17.610244:   - 发现标签数量: 0
2025-08-26 11:49:17.611243:   - 未发现任何RFID标签
2025-08-26 11:49:17.611243: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:17.611243: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:18.091234: 🔄 开始RFID轮询检查...
2025-08-26 11:49:18.091234: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:18.091234: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:18.108235: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:18.108235:   - 设备句柄: 2395416852128
2025-08-26 11:49:18.109235:   - FetchRecords返回值: 0
2025-08-26 11:49:18.109235:   - 报告数量: 0
2025-08-26 11:49:18.109235: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:18.110235:   - 发现标签数量: 0
2025-08-26 11:49:18.110235:   - 未发现任何RFID标签
2025-08-26 11:49:18.110235: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:18.110235: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:18.591226: 🔄 开始RFID轮询检查...
2025-08-26 11:49:18.591226: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:18.592226: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:18.608225: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:18.608225:   - 设备句柄: 2395416852128
2025-08-26 11:49:18.609225:   - FetchRecords返回值: 0
2025-08-26 11:49:18.609225:   - 报告数量: 0
2025-08-26 11:49:18.609225: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:18.609225:   - 发现标签数量: 0
2025-08-26 11:49:18.609225:   - 未发现任何RFID标签
2025-08-26 11:49:18.610225: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:18.610225: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:19.091216: 🔄 开始RFID轮询检查...
2025-08-26 11:49:19.091216: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:19.091216: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:19.109216: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:19.109216:   - 设备句柄: 2395416852128
2025-08-26 11:49:19.110217:   - FetchRecords返回值: 0
2025-08-26 11:49:19.110217:   - 报告数量: 0
2025-08-26 11:49:19.110217: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:19.110217:   - 发现标签数量: 0
2025-08-26 11:49:19.111218:   - 未发现任何RFID标签
2025-08-26 11:49:19.111218: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:19.111218: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:19.590209: 🔄 开始RFID轮询检查...
2025-08-26 11:49:19.590209: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:19.591211: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:19.610208: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:19.610208:   - 设备句柄: 2395416852128
2025-08-26 11:49:19.611208:   - FetchRecords返回值: 0
2025-08-26 11:49:19.611208:   - 报告数量: 0
2025-08-26 11:49:19.611208: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:19.611208:   - 发现标签数量: 0
2025-08-26 11:49:19.611208:   - 未发现任何RFID标签
2025-08-26 11:49:19.612208: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:19.612208: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:20.091200: 🔄 开始RFID轮询检查...
2025-08-26 11:49:20.091200: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:20.092202: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:20.108199: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:20.108199:   - 设备句柄: 2395416852128
2025-08-26 11:49:20.109199:   - FetchRecords返回值: 0
2025-08-26 11:49:20.109199:   - 报告数量: 0
2025-08-26 11:49:20.109199: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:20.109199:   - 发现标签数量: 0
2025-08-26 11:49:20.110199:   - 未发现任何RFID标签
2025-08-26 11:49:20.110199: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:20.110199: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:20.591190: 🔄 开始RFID轮询检查...
2025-08-26 11:49:20.591190: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:20.591190: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:20.608191: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:20.608191:   - 设备句柄: 2395416852128
2025-08-26 11:49:20.609192:   - FetchRecords返回值: 0
2025-08-26 11:49:20.609192:   - 报告数量: 0
2025-08-26 11:49:20.609192: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:20.609192:   - 发现标签数量: 0
2025-08-26 11:49:20.609192:   - 未发现任何RFID标签
2025-08-26 11:49:20.610191: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:20.610191: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:21.091182: 🔄 开始RFID轮询检查...
2025-08-26 11:49:21.091182: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:21.092200: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:21.108182: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:21.108182:   - 设备句柄: 2395416852128
2025-08-26 11:49:21.109182:   - FetchRecords返回值: 0
2025-08-26 11:49:21.109182:   - 报告数量: 0
2025-08-26 11:49:21.109182: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:21.109182:   - 发现标签数量: 0
2025-08-26 11:49:21.110181:   - 未发现任何RFID标签
2025-08-26 11:49:21.110181: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:21.110181: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:21.591175: 🔄 开始RFID轮询检查...
2025-08-26 11:49:21.592174: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:21.592174: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:21.608174: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:21.609174:   - 设备句柄: 2395416852128
2025-08-26 11:49:21.610176:   - FetchRecords返回值: 0
2025-08-26 11:49:21.610176:   - 报告数量: 0
2025-08-26 11:49:21.610176: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:21.611178:   - 发现标签数量: 0
2025-08-26 11:49:21.611178:   - 未发现任何RFID标签
2025-08-26 11:49:21.611178: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:21.611178: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:22.091164: 🔄 开始RFID轮询检查...
2025-08-26 11:49:22.091164: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:22.092165: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:22.108164: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:22.109170:   - 设备句柄: 2395416852128
2025-08-26 11:49:22.109170:   - FetchRecords返回值: 0
2025-08-26 11:49:22.109170:   - 报告数量: 0
2025-08-26 11:49:22.109170: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:22.110164:   - 发现标签数量: 0
2025-08-26 11:49:22.110164:   - 未发现任何RFID标签
2025-08-26 11:49:22.110164: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:22.111165: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:22.591157: 🔄 开始RFID轮询检查...
2025-08-26 11:49:22.591157: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:22.592158: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:22.609156: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:22.610159:   - 设备句柄: 2395416852128
2025-08-26 11:49:22.610159:   - FetchRecords返回值: 0
2025-08-26 11:49:22.611156:   - 报告数量: 0
2025-08-26 11:49:22.611156: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:22.611156:   - 发现标签数量: 0
2025-08-26 11:49:22.612174:   - 未发现任何RFID标签
2025-08-26 11:49:22.612174: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:22.613156: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:23.091147: 🔄 开始RFID轮询检查...
2025-08-26 11:49:23.091147: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:23.091147: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:23.108147: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:23.109154:   - 设备句柄: 2395416852128
2025-08-26 11:49:23.109154:   - FetchRecords返回值: 0
2025-08-26 11:49:23.110165:   - 报告数量: 0
2025-08-26 11:49:23.110165: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:23.110165:   - 发现标签数量: 0
2025-08-26 11:49:23.111148:   - 未发现任何RFID标签
2025-08-26 11:49:23.111148: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:23.111148: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:23.591139: 🔄 开始RFID轮询检查...
2025-08-26 11:49:23.591139: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:23.592140: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:23.609139: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:23.610140:   - 设备句柄: 2395416852128
2025-08-26 11:49:23.610140:   - FetchRecords返回值: 0
2025-08-26 11:49:23.610140:   - 报告数量: 0
2025-08-26 11:49:23.610140: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:23.610140:   - 发现标签数量: 0
2025-08-26 11:49:23.611143:   - 未发现任何RFID标签
2025-08-26 11:49:23.611143: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:23.611143: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:24.091130: 🔄 开始RFID轮询检查...
2025-08-26 11:49:24.091130: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:24.092134: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:24.109129: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:24.109129:   - 设备句柄: 2395416852128
2025-08-26 11:49:24.109129:   - FetchRecords返回值: 0
2025-08-26 11:49:24.110129:   - 报告数量: 0
2025-08-26 11:49:24.110129: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:24.110129:   - 发现标签数量: 0
2025-08-26 11:49:24.110129:   - 未发现任何RFID标签
2025-08-26 11:49:24.110129: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:24.110129: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:24.591122: 🔄 开始RFID轮询检查...
2025-08-26 11:49:24.591122: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:24.591122: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:24.609121: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:24.610122:   - 设备句柄: 2395416852128
2025-08-26 11:49:24.610122:   - FetchRecords返回值: 0
2025-08-26 11:49:24.610122:   - 报告数量: 0
2025-08-26 11:49:24.611122: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:24.611122:   - 发现标签数量: 0
2025-08-26 11:49:24.611122:   - 未发现任何RFID标签
2025-08-26 11:49:24.611122: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:24.611122: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:24.770118: ✅ API配置保存成功: C:\Users\<USER>\AppData\Roaming\com.example\a3g\config\api_config.json
2025-08-26 11:49:24.770118: 设备API服务初始化完成: http://***************:9000
2025-08-26 11:49:25.091113: 🔄 开始RFID轮询检查...
2025-08-26 11:49:25.091113: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:25.091113: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:25.109112: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:25.109112:   - 设备句柄: 2395416852128
2025-08-26 11:49:25.109112:   - FetchRecords返回值: 0
2025-08-26 11:49:25.110113:   - 报告数量: 0
2025-08-26 11:49:25.110113: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:25.110113:   - 发现标签数量: 0
2025-08-26 11:49:25.110113:   - 未发现任何RFID标签
2025-08-26 11:49:25.110113: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:25.111112: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:25.591106: 🔄 开始RFID轮询检查...
2025-08-26 11:49:25.592107: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:25.592107: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:25.609103: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:25.609103:   - 设备句柄: 2395416852128
2025-08-26 11:49:25.610104:   - FetchRecords返回值: 0
2025-08-26 11:49:25.610104:   - 报告数量: 0
2025-08-26 11:49:25.610104: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:25.611103:   - 发现标签数量: 0
2025-08-26 11:49:25.611103:   - 未发现任何RFID标签
2025-08-26 11:49:25.611103: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:25.611103: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:26.091097: 🔄 开始RFID轮询检查...
2025-08-26 11:49:26.091097: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:26.092098: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:26.109095: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:26.109095:   - 设备句柄: 2395416852128
2025-08-26 11:49:26.109095:   - FetchRecords返回值: 0
2025-08-26 11:49:26.110094:   - 报告数量: 0
2025-08-26 11:49:26.110094: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:26.110094:   - 发现标签数量: 0
2025-08-26 11:49:26.110094:   - 未发现任何RFID标签
2025-08-26 11:49:26.110094: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:26.110094: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:26.591087: 🔄 开始RFID轮询检查...
2025-08-26 11:49:26.591087: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:26.592091: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:26.609088: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:26.610086:   - 设备句柄: 2395416852128
2025-08-26 11:49:26.610086:   - FetchRecords返回值: 0
2025-08-26 11:49:26.610086:   - 报告数量: 0
2025-08-26 11:49:26.610086: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:26.610086:   - 发现标签数量: 0
2025-08-26 11:49:26.610086:   - 未发现任何RFID标签
2025-08-26 11:49:26.611086: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:26.611086: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:27.091077: 🔄 开始RFID轮询检查...
2025-08-26 11:49:27.092077: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:27.093081: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:27.108077: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:27.108077:   - 设备句柄: 2395416852128
2025-08-26 11:49:27.109078:   - FetchRecords返回值: 0
2025-08-26 11:49:27.109078:   - 报告数量: 0
2025-08-26 11:49:27.109078: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:27.109078:   - 发现标签数量: 0
2025-08-26 11:49:27.109078:   - 未发现任何RFID标签
2025-08-26 11:49:27.110077: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:27.110077: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:27.591068: 🔄 开始RFID轮询检查...
2025-08-26 11:49:27.591068: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:27.591068: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:27.608069: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:27.608069:   - 设备句柄: 2395416852128
2025-08-26 11:49:27.608069:   - FetchRecords返回值: 0
2025-08-26 11:49:27.609069:   - 报告数量: 0
2025-08-26 11:49:27.609069: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:27.609069:   - 发现标签数量: 0
2025-08-26 11:49:27.609069:   - 未发现任何RFID标签
2025-08-26 11:49:27.609069: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:27.609069: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:28.091059: 🔄 开始RFID轮询检查...
2025-08-26 11:49:28.091059: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:28.091059: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:28.108060: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:28.109061:   - 设备句柄: 2395416852128
2025-08-26 11:49:28.109061:   - FetchRecords返回值: 0
2025-08-26 11:49:28.109061:   - 报告数量: 0
2025-08-26 11:49:28.110060: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:28.110060:   - 发现标签数量: 0
2025-08-26 11:49:28.110060:   - 未发现任何RFID标签
2025-08-26 11:49:28.111060: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:28.111060: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:28.232058: 设备API服务初始化完成: http://***************:9000
2025-08-26 11:49:28.591051: 🔄 开始RFID轮询检查...
2025-08-26 11:49:28.591051: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:28.591051: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:28.608051: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:28.608051:   - 设备句柄: 2395416852128
2025-08-26 11:49:28.609052:   - FetchRecords返回值: 0
2025-08-26 11:49:28.609052:   - 报告数量: 0
2025-08-26 11:49:28.609052: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:28.609052:   - 发现标签数量: 0
2025-08-26 11:49:28.609052:   - 未发现任何RFID标签
2025-08-26 11:49:28.610051: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:28.610051: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:29.091050: 🔄 开始RFID轮询检查...
2025-08-26 11:49:29.092045: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:29.093045: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:29.108042: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:29.108042:   - 设备句柄: 2395416852128
2025-08-26 11:49:29.109042:   - FetchRecords返回值: 0
2025-08-26 11:49:29.109042:   - 报告数量: 0
2025-08-26 11:49:29.109042: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:29.109042:   - 发现标签数量: 0
2025-08-26 11:49:29.109042:   - 未发现任何RFID标签
2025-08-26 11:49:29.110044: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:29.110044: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:29.591034: 🔄 开始RFID轮询检查...
2025-08-26 11:49:29.591034: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:29.591034: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:29.608034: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:29.610035:   - 设备句柄: 2395416852128
2025-08-26 11:49:29.611038:   - FetchRecords返回值: 0
2025-08-26 11:49:29.611038:   - 报告数量: 0
2025-08-26 11:49:29.611038: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:29.612034:   - 发现标签数量: 0
2025-08-26 11:49:29.612034:   - 未发现任何RFID标签
2025-08-26 11:49:29.612034: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:29.612034: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:30.091025: 🔄 开始RFID轮询检查...
2025-08-26 11:49:30.091025: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:30.092026: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:30.108025: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:30.109032:   - 设备句柄: 2395416852128
2025-08-26 11:49:30.110027:   - FetchRecords返回值: 0
2025-08-26 11:49:30.111026:   - 报告数量: 0
2025-08-26 11:49:30.111026: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:30.111026:   - 发现标签数量: 0
2025-08-26 11:49:30.112025:   - 未发现任何RFID标签
2025-08-26 11:49:30.112025: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:30.112025: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:30.591018: 🔄 开始RFID轮询检查...
2025-08-26 11:49:30.591018: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:30.592023: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:30.609027: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:30.610017:   - 设备句柄: 2395416852128
2025-08-26 11:49:30.610017:   - FetchRecords返回值: 0
2025-08-26 11:49:30.610017:   - 报告数量: 0
2025-08-26 11:49:30.610017: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:30.610017:   - 发现标签数量: 0
2025-08-26 11:49:30.610017:   - 未发现任何RFID标签
2025-08-26 11:49:30.611016: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:30.611016: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:31.091008: 🔄 开始RFID轮询检查...
2025-08-26 11:49:31.091008: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:31.091008: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:31.109007: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:31.109007:   - 设备句柄: 2395416852128
2025-08-26 11:49:31.109007:   - FetchRecords返回值: 0
2025-08-26 11:49:31.110008:   - 报告数量: 0
2025-08-26 11:49:31.110008: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:31.110008:   - 发现标签数量: 0
2025-08-26 11:49:31.110008:   - 未发现任何RFID标签
2025-08-26 11:49:31.110008: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:31.111008: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:31.589999: 🔄 开始RFID轮询检查...
2025-08-26 11:49:31.589999: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:31.589999: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:31.607999: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:31.608999:   - 设备句柄: 2395416852128
2025-08-26 11:49:31.608999:   - FetchRecords返回值: 0
2025-08-26 11:49:31.608999:   - 报告数量: 0
2025-08-26 11:49:31.610: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:31.610:   - 发现标签数量: 0
2025-08-26 11:49:31.611:   - 未发现任何RFID标签
2025-08-26 11:49:31.611: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:31.611: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:32.090993: 🔄 开始RFID轮询检查...
2025-08-26 11:49:32.090993: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:32.091992: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:32.108994: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:32.109990:   - 设备句柄: 2395416852128
2025-08-26 11:49:32.109990:   - FetchRecords返回值: 0
2025-08-26 11:49:32.109990:   - 报告数量: 0
2025-08-26 11:49:32.110992: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:32.110992:   - 发现标签数量: 0
2025-08-26 11:49:32.110992:   - 未发现任何RFID标签
2025-08-26 11:49:32.110992: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:32.110992: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:32.590981: 🔄 开始RFID轮询检查...
2025-08-26 11:49:32.590981: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:32.590981: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:32.608981: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:32.608981:   - 设备句柄: 2395416852128
2025-08-26 11:49:32.609982:   - FetchRecords返回值: 0
2025-08-26 11:49:32.609982:   - 报告数量: 0
2025-08-26 11:49:32.609982: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:32.609982:   - 发现标签数量: 0
2025-08-26 11:49:32.609982:   - 未发现任何RFID标签
2025-08-26 11:49:32.609982: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:32.610981: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:33.090974: 🔄 开始RFID轮询检查...
2025-08-26 11:49:33.090974: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:33.091974: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:33.108972: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:33.108972:   - 设备句柄: 2395416852128
2025-08-26 11:49:33.108972:   - FetchRecords返回值: 0
2025-08-26 11:49:33.109973:   - 报告数量: 0
2025-08-26 11:49:33.109973: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:33.109973:   - 发现标签数量: 0
2025-08-26 11:49:33.109973:   - 未发现任何RFID标签
2025-08-26 11:49:33.109973: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:33.109973: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:33.590964: 🔄 开始RFID轮询检查...
2025-08-26 11:49:33.590964: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:33.591965: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:33.607966: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:33.608965:   - 设备句柄: 2395416852128
2025-08-26 11:49:33.608965:   - FetchRecords返回值: 0
2025-08-26 11:49:33.609966:   - 报告数量: 0
2025-08-26 11:49:33.609966: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:33.609966:   - 发现标签数量: 0
2025-08-26 11:49:33.609966:   - 未发现任何RFID标签
2025-08-26 11:49:33.610964: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:33.610964: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:34.090955: 🔄 开始RFID轮询检查...
2025-08-26 11:49:34.090955: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:34.091957: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:34.108955: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:34.108955:   - 设备句柄: 2395416852128
2025-08-26 11:49:34.108955:   - FetchRecords返回值: 0
2025-08-26 11:49:34.109955:   - 报告数量: 0
2025-08-26 11:49:34.109955: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:34.109955:   - 发现标签数量: 0
2025-08-26 11:49:34.109955:   - 未发现任何RFID标签
2025-08-26 11:49:34.109955: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:34.109955: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:34.590947: 🔄 开始RFID轮询检查...
2025-08-26 11:49:34.590947: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:34.590947: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:34.608949: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:34.609948:   - 设备句柄: 2395416852128
2025-08-26 11:49:34.609948:   - FetchRecords返回值: 0
2025-08-26 11:49:34.610948:   - 报告数量: 0
2025-08-26 11:49:34.610948: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:34.610948:   - 发现标签数量: 0
2025-08-26 11:49:34.611947:   - 未发现任何RFID标签
2025-08-26 11:49:34.611947: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:34.611947: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:35.090940: 🔄 开始RFID轮询检查...
2025-08-26 11:49:35.090940: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:35.091940: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:35.108938: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:35.109938:   - 设备句柄: 2395416852128
2025-08-26 11:49:35.109938:   - FetchRecords返回值: 0
2025-08-26 11:49:35.109938:   - 报告数量: 0
2025-08-26 11:49:35.109938: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:35.109938:   - 发现标签数量: 0
2025-08-26 11:49:35.110938:   - 未发现任何RFID标签
2025-08-26 11:49:35.110938: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:35.110938: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:35.590929: 🔄 开始RFID轮询检查...
2025-08-26 11:49:35.590929: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:35.590929: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:35.607929: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:35.607929:   - 设备句柄: 2395416852128
2025-08-26 11:49:35.608930:   - FetchRecords返回值: 0
2025-08-26 11:49:35.608930:   - 报告数量: 0
2025-08-26 11:49:35.608930: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:35.608930:   - 发现标签数量: 0
2025-08-26 11:49:35.608930:   - 未发现任何RFID标签
2025-08-26 11:49:35.609929: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:35.609929: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:36.090922: 🔄 开始RFID轮询检查...
2025-08-26 11:49:36.090922: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:36.091924: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:36.108921: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:36.109923:   - 设备句柄: 2395416852128
2025-08-26 11:49:36.109923:   - FetchRecords返回值: 0
2025-08-26 11:49:36.110924:   - 报告数量: 0
2025-08-26 11:49:36.110924: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:36.111922:   - 发现标签数量: 0
2025-08-26 11:49:36.111922:   - 未发现任何RFID标签
2025-08-26 11:49:36.111922: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:36.112921: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:36.590912: 🔄 开始RFID轮询检查...
2025-08-26 11:49:36.590912: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:36.590912: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:36.609916: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:36.610912:   - 设备句柄: 2395416852128
2025-08-26 11:49:36.610912:   - FetchRecords返回值: 0
2025-08-26 11:49:36.610912:   - 报告数量: 0
2025-08-26 11:49:36.611913: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:36.611913:   - 发现标签数量: 0
2025-08-26 11:49:36.611913:   - 未发现任何RFID标签
2025-08-26 11:49:36.611913: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:36.611913: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:37.090903: 🔄 开始RFID轮询检查...
2025-08-26 11:49:37.090903: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:37.090903: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:37.107904: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:37.108904:   - 设备句柄: 2395416852128
2025-08-26 11:49:37.108904:   - FetchRecords返回值: 0
2025-08-26 11:49:37.108904:   - 报告数量: 0
2025-08-26 11:49:37.109903: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:37.109903:   - 发现标签数量: 0
2025-08-26 11:49:37.110904:   - 未发现任何RFID标签
2025-08-26 11:49:37.110904: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:37.110904: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:37.537897: 📖 读取表单字段: device_register.device_mac = FF-FF-FF-FF-FF-FF
2025-08-26 11:49:37.537897: 📖 反显字段值: device_register.device_mac = FF-FF-FF-FF-FF-FF
2025-08-26 11:49:37.538902: 📖 读取表单字段: device_register.device_name = 
2025-08-26 11:49:37.538902: 📖 读取表单字段: device_register.door_code = 
2025-08-26 11:49:37.538902: 📖 读取表单字段: device_register.door_name = 
2025-08-26 11:49:37.538902: 📖 读取表单字段: device_register.area_code = 
2025-08-26 11:49:37.539896: 📖 读取表单字段: device_register.area_name = 
2025-08-26 11:49:37.539896: 📖 读取表单字段: device_register.lib_id = 
2025-08-26 11:49:37.539896: 📖 读取表单字段: device_register.lib_name = 
2025-08-26 11:49:37.539896: 📖 读取表单字段: device_register.location = 
2025-08-26 11:49:37.539896: 📖 读取表单字段: device_register.description = 
2025-08-26 11:49:37.539896: 📖 读取表单字段: device_register.ip_address = 
2025-08-26 11:49:37.540897: 📖 读取表单字段: device_register.port = 
2025-08-26 11:49:37.540897: 设备API服务初始化完成: http://***************:9000
2025-08-26 11:49:37.590895: 🔄 开始RFID轮询检查...
2025-08-26 11:49:37.590895: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:37.591897: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:37.608897: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:37.608897:   - 设备句柄: 2395416852128
2025-08-26 11:49:37.609895:   - FetchRecords返回值: 0
2025-08-26 11:49:37.609895:   - 报告数量: 0
2025-08-26 11:49:37.609895: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:37.609895:   - 发现标签数量: 0
2025-08-26 11:49:37.609895:   - 未发现任何RFID标签
2025-08-26 11:49:37.609895: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:37.610894: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:38.090886: 🔄 开始RFID轮询检查...
2025-08-26 11:49:38.091888: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:38.091888: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:38.108885: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:38.108885:   - 设备句柄: 2395416852128
2025-08-26 11:49:38.109886:   - FetchRecords返回值: 0
2025-08-26 11:49:38.109886:   - 报告数量: 0
2025-08-26 11:49:38.109886: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:38.109886:   - 发现标签数量: 0
2025-08-26 11:49:38.109886:   - 未发现任何RFID标签
2025-08-26 11:49:38.110886: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:38.110886: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:38.590878: 🔄 开始RFID轮询检查...
2025-08-26 11:49:38.590878: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:38.591879: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:38.608877: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:38.608877:   - 设备句柄: 2395416852128
2025-08-26 11:49:38.609877:   - FetchRecords返回值: 0
2025-08-26 11:49:38.609877:   - 报告数量: 0
2025-08-26 11:49:38.609877: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:38.609877:   - 发现标签数量: 0
2025-08-26 11:49:38.610879:   - 未发现任何RFID标签
2025-08-26 11:49:38.610879: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:38.610879: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:39.090869: 🔄 开始RFID轮询检查...
2025-08-26 11:49:39.090869: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:39.090869: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:39.109868: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:39.109868:   - 设备句柄: 2395416852128
2025-08-26 11:49:39.109868:   - FetchRecords返回值: 0
2025-08-26 11:49:39.109868:   - 报告数量: 0
2025-08-26 11:49:39.109868: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:39.110868:   - 发现标签数量: 0
2025-08-26 11:49:39.110868:   - 未发现任何RFID标签
2025-08-26 11:49:39.110868: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:39.110868: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:39.590860: 🔄 开始RFID轮询检查...
2025-08-26 11:49:39.590860: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:39.590860: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:39.608860: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:39.609861:   - 设备句柄: 2395416852128
2025-08-26 11:49:39.609861:   - FetchRecords返回值: 0
2025-08-26 11:49:39.610860:   - 报告数量: 0
2025-08-26 11:49:39.610860: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:39.610860:   - 发现标签数量: 0
2025-08-26 11:49:39.610860:   - 未发现任何RFID标签
2025-08-26 11:49:39.611860: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:39.611860: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:40.090853: 🔄 开始RFID轮询检查...
2025-08-26 11:49:40.090853: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:40.091853: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:40.108854: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:40.109852:   - 设备句柄: 2395416852128
2025-08-26 11:49:40.109852:   - FetchRecords返回值: 0
2025-08-26 11:49:40.109852:   - 报告数量: 0
2025-08-26 11:49:40.109852: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:40.110858:   - 发现标签数量: 0
2025-08-26 11:49:40.110858:   - 未发现任何RFID标签
2025-08-26 11:49:40.110858: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:40.110858: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:40.511845: 📖 读取表单字段: device_register.device_mac = FF-FF-FF-FF-FF-FF
2025-08-26 11:49:40.512845: 📖 反显字段值: device_register.device_mac = FF-FF-FF-FF-FF-FF
2025-08-26 11:49:40.512845: 📖 读取表单字段: device_register.device_name = 
2025-08-26 11:49:40.512845: 📖 读取表单字段: device_register.door_code = 
2025-08-26 11:49:40.513844: 📖 读取表单字段: device_register.door_name = 
2025-08-26 11:49:40.513844: 📖 读取表单字段: device_register.area_code = 
2025-08-26 11:49:40.513844: 📖 读取表单字段: device_register.area_name = 
2025-08-26 11:49:40.513844: 📖 读取表单字段: device_register.lib_id = 
2025-08-26 11:49:40.513844: 📖 读取表单字段: device_register.lib_name = 
2025-08-26 11:49:40.513844: 📖 读取表单字段: device_register.location = 
2025-08-26 11:49:40.514844: 📖 读取表单字段: device_register.description = 
2025-08-26 11:49:40.514844: 📖 读取表单字段: device_register.ip_address = 
2025-08-26 11:49:40.514844: 📖 读取表单字段: device_register.port = 
2025-08-26 11:49:40.590842: 🔄 开始RFID轮询检查...
2025-08-26 11:49:40.590842: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:40.591842: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:40.608842: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:40.609843:   - 设备句柄: 2395416852128
2025-08-26 11:49:40.609843:   - FetchRecords返回值: 0
2025-08-26 11:49:40.609843:   - 报告数量: 0
2025-08-26 11:49:40.610842: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:40.610842:   - 发现标签数量: 0
2025-08-26 11:49:40.610842:   - 未发现任何RFID标签
2025-08-26 11:49:40.610842: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:40.610842: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:41.090834: 🔄 开始RFID轮询检查...
2025-08-26 11:49:41.090834: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:41.091834: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:41.108834: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:41.109835:   - 设备句柄: 2395416852128
2025-08-26 11:49:41.109835:   - FetchRecords返回值: 0
2025-08-26 11:49:41.110834:   - 报告数量: 0
2025-08-26 11:49:41.110834: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:41.110834:   - 发现标签数量: 0
2025-08-26 11:49:41.111834:   - 未发现任何RFID标签
2025-08-26 11:49:41.111834: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:41.111834: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:41.590824: 🔄 开始RFID轮询检查...
2025-08-26 11:49:41.590824: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:41.590824: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:41.607826: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:41.608825:   - 设备句柄: 2395416852128
2025-08-26 11:49:41.608825:   - FetchRecords返回值: 0
2025-08-26 11:49:41.608825:   - 报告数量: 0
2025-08-26 11:49:41.609825: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:41.609825:   - 发现标签数量: 0
2025-08-26 11:49:41.610826:   - 未发现任何RFID标签
2025-08-26 11:49:41.610826: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:41.610826: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:42.090816: 🔄 开始RFID轮询检查...
2025-08-26 11:49:42.090816: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:42.091818: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:42.108816: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:42.108816:   - 设备句柄: 2395416852128
2025-08-26 11:49:42.109816:   - FetchRecords返回值: 0
2025-08-26 11:49:42.109816:   - 报告数量: 0
2025-08-26 11:49:42.109816: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:42.109816:   - 发现标签数量: 0
2025-08-26 11:49:42.110817:   - 未发现任何RFID标签
2025-08-26 11:49:42.110817: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:42.110817: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:42.589809: 🔄 开始RFID轮询检查...
2025-08-26 11:49:42.590812: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:42.590812: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:42.608807: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:42.608807:   - 设备句柄: 2395416852128
2025-08-26 11:49:42.608807:   - FetchRecords返回值: 0
2025-08-26 11:49:42.608807:   - 报告数量: 0
2025-08-26 11:49:42.609807: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:42.609807:   - 发现标签数量: 0
2025-08-26 11:49:42.609807:   - 未发现任何RFID标签
2025-08-26 11:49:42.609807: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:42.609807: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:43.089799: 🔄 开始RFID轮询检查...
2025-08-26 11:49:43.089799: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:43.089799: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:43.107802: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:43.107802:   - 设备句柄: 2395416852128
2025-08-26 11:49:43.108799:   - FetchRecords返回值: 0
2025-08-26 11:49:43.108799:   - 报告数量: 0
2025-08-26 11:49:43.108799: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:43.108799:   - 发现标签数量: 0
2025-08-26 11:49:43.108799:   - 未发现任何RFID标签
2025-08-26 11:49:43.108799: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:43.109798: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:43.590790: 🔄 开始RFID轮询检查...
2025-08-26 11:49:43.590790: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:43.590790: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:43.607791: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:43.608792:   - 设备句柄: 2395416852128
2025-08-26 11:49:43.609791:   - FetchRecords返回值: 0
2025-08-26 11:49:43.609791:   - 报告数量: 0
2025-08-26 11:49:43.609791: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:43.610790:   - 发现标签数量: 0
2025-08-26 11:49:43.610790:   - 未发现任何RFID标签
2025-08-26 11:49:43.610790: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:43.610790: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:44.089790: 🔄 开始RFID轮询检查...
2025-08-26 11:49:44.090784: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:44.091784: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:44.108781: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:44.108781:   - 设备句柄: 2395416852128
2025-08-26 11:49:44.109781:   - FetchRecords返回值: 0
2025-08-26 11:49:44.109781:   - 报告数量: 0
2025-08-26 11:49:44.109781: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:44.109781:   - 发现标签数量: 0
2025-08-26 11:49:44.109781:   - 未发现任何RFID标签
2025-08-26 11:49:44.109781: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:44.109781: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:44.590772: 🔄 开始RFID轮询检查...
2025-08-26 11:49:44.590772: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:44.591773: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:44.607772: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:44.607772:   - 设备句柄: 2395416852128
2025-08-26 11:49:44.608775:   - FetchRecords返回值: 0
2025-08-26 11:49:44.608775:   - 报告数量: 0
2025-08-26 11:49:44.608775: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:44.609779:   - 发现标签数量: 0
2025-08-26 11:49:44.609779:   - 未发现任何RFID标签
2025-08-26 11:49:44.610779: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:44.610779: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:45.089765: 🔄 开始RFID轮询检查...
2025-08-26 11:49:45.089765: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:45.090765: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:45.108768: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:45.109764:   - 设备句柄: 2395416852128
2025-08-26 11:49:45.109764:   - FetchRecords返回值: 0
2025-08-26 11:49:45.109764:   - 报告数量: 0
2025-08-26 11:49:45.109764: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:45.110764:   - 发现标签数量: 0
2025-08-26 11:49:45.110764:   - 未发现任何RFID标签
2025-08-26 11:49:45.110764: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:45.110764: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:45.590755: 🔄 开始RFID轮询检查...
2025-08-26 11:49:45.590755: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:45.591758: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:45.608755: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:45.608755:   - 设备句柄: 2395416852128
2025-08-26 11:49:45.608755:   - FetchRecords返回值: 0
2025-08-26 11:49:45.609755:   - 报告数量: 0
2025-08-26 11:49:45.609755: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:45.609755:   - 发现标签数量: 0
2025-08-26 11:49:45.609755:   - 未发现任何RFID标签
2025-08-26 11:49:45.609755: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:45.610755: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:46.090746: 🔄 开始RFID轮询检查...
2025-08-26 11:49:46.090746: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:46.090746: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:46.107747: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:46.108748:   - 设备句柄: 2395416852128
2025-08-26 11:49:46.108748:   - FetchRecords返回值: 0
2025-08-26 11:49:46.108748:   - 报告数量: 0
2025-08-26 11:49:46.109747: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:46.109747:   - 发现标签数量: 0
2025-08-26 11:49:46.109747:   - 未发现任何RFID标签
2025-08-26 11:49:46.110748: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:46.110748: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:46.590739: 🔄 开始RFID轮询检查...
2025-08-26 11:49:46.591739: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:46.591739: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:46.607742: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:46.608740:   - 设备句柄: 2395416852128
2025-08-26 11:49:46.608740:   - FetchRecords返回值: 0
2025-08-26 11:49:46.608740:   - 报告数量: 0
2025-08-26 11:49:46.608740: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:46.609738:   - 发现标签数量: 0
2025-08-26 11:49:46.609738:   - 未发现任何RFID标签
2025-08-26 11:49:46.609738: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:46.609738: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:47.090729: 🔄 开始RFID轮询检查...
2025-08-26 11:49:47.090729: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:47.090729: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:47.108728: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:47.108728:   - 设备句柄: 2395416852128
2025-08-26 11:49:47.109729:   - FetchRecords返回值: 0
2025-08-26 11:49:47.109729:   - 报告数量: 0
2025-08-26 11:49:47.109729: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:47.109729:   - 发现标签数量: 0
2025-08-26 11:49:47.109729:   - 未发现任何RFID标签
2025-08-26 11:49:47.109729: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:47.110729: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:47.590721: 🔄 开始RFID轮询检查...
2025-08-26 11:49:47.590721: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:47.591721: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:47.607720: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:47.607720:   - 设备句柄: 2395416852128
2025-08-26 11:49:47.608721:   - FetchRecords返回值: 0
2025-08-26 11:49:47.608721:   - 报告数量: 0
2025-08-26 11:49:47.608721: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:47.608721:   - 发现标签数量: 0
2025-08-26 11:49:47.608721:   - 未发现任何RFID标签
2025-08-26 11:49:47.608721: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:47.609725: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:48.089722: 🔄 开始RFID轮询检查...
2025-08-26 11:49:48.090715: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:48.090715: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:48.108711: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:48.108711:   - 设备句柄: 2395416852128
2025-08-26 11:49:48.108711:   - FetchRecords返回值: 0
2025-08-26 11:49:48.108711:   - 报告数量: 0
2025-08-26 11:49:48.109712: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:48.109712:   - 发现标签数量: 0
2025-08-26 11:49:48.109712:   - 未发现任何RFID标签
2025-08-26 11:49:48.109712: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:48.109712: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:48.590703: 🔄 开始RFID轮询检查...
2025-08-26 11:49:48.590703: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:48.590703: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:48.607703: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:48.607703:   - 设备句柄: 2395416852128
2025-08-26 11:49:48.608703:   - FetchRecords返回值: 0
2025-08-26 11:49:48.608703:   - 报告数量: 0
2025-08-26 11:49:48.608703: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:48.609703:   - 发现标签数量: 0
2025-08-26 11:49:48.609703:   - 未发现任何RFID标签
2025-08-26 11:49:48.609703: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:48.609703: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:49.090697: 🔄 开始RFID轮询检查...
2025-08-26 11:49:49.091700: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:49.091700: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:49.108695: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:49.108695:   - 设备句柄: 2395416852128
2025-08-26 11:49:49.109695:   - FetchRecords返回值: 0
2025-08-26 11:49:49.109695:   - 报告数量: 0
2025-08-26 11:49:49.109695: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:49.109695:   - 发现标签数量: 0
2025-08-26 11:49:49.110695:   - 未发现任何RFID标签
2025-08-26 11:49:49.110695: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:49.110695: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:49.590685: 🔄 开始RFID轮询检查...
2025-08-26 11:49:49.590685: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:49.590685: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:49.607686: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:49.608686:   - 设备句柄: 2395416852128
2025-08-26 11:49:49.608686:   - FetchRecords返回值: 0
2025-08-26 11:49:49.608686:   - 报告数量: 0
2025-08-26 11:49:49.609686: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:49.609686:   - 发现标签数量: 0
2025-08-26 11:49:49.610686:   - 未发现任何RFID标签
2025-08-26 11:49:49.610686: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:49.610686: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:50.090676: 🔄 开始RFID轮询检查...
2025-08-26 11:49:50.090676: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:50.091677: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:50.107676: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:50.107676:   - 设备句柄: 2395416852128
2025-08-26 11:49:50.108677:   - FetchRecords返回值: 0
2025-08-26 11:49:50.108677:   - 报告数量: 0
2025-08-26 11:49:50.108677: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:50.108677:   - 发现标签数量: 0
2025-08-26 11:49:50.109677:   - 未发现任何RFID标签
2025-08-26 11:49:50.109677: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:50.109677: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:50.590668: 🔄 开始RFID轮询检查...
2025-08-26 11:49:50.590668: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:50.591669: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:50.607668: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:50.608670:   - 设备句柄: 2395416852128
2025-08-26 11:49:50.608670:   - FetchRecords返回值: 0
2025-08-26 11:49:50.609670:   - 报告数量: 0
2025-08-26 11:49:50.609670: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:50.610670:   - 发现标签数量: 0
2025-08-26 11:49:50.610670:   - 未发现任何RFID标签
2025-08-26 11:49:50.611670: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:50.611670: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:51.089660: 🔄 开始RFID轮询检查...
2025-08-26 11:49:51.089660: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:51.090663: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:51.108659: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:51.109660:   - 设备句柄: 2395416852128
2025-08-26 11:49:51.109660:   - FetchRecords返回值: 0
2025-08-26 11:49:51.109660:   - 报告数量: 0
2025-08-26 11:49:51.110664: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:51.110664:   - 发现标签数量: 0
2025-08-26 11:49:51.110664:   - 未发现任何RFID标签
2025-08-26 11:49:51.110664: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:51.111667: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:51.590652: 🔄 开始RFID轮询检查...
2025-08-26 11:49:51.591653: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:51.591653: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:51.608651: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:51.609651:   - 设备句柄: 2395416852128
2025-08-26 11:49:51.609651:   - FetchRecords返回值: 0
2025-08-26 11:49:51.609651:   - 报告数量: 0
2025-08-26 11:49:51.610651: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:51.610651:   - 发现标签数量: 0
2025-08-26 11:49:51.610651:   - 未发现任何RFID标签
2025-08-26 11:49:51.611652: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:51.611652: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:52.089646: 🔄 开始RFID轮询检查...
2025-08-26 11:49:52.090645: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:52.090645: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:52.108643: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:52.109642:   - 设备句柄: 2395416852128
2025-08-26 11:49:52.109642:   - FetchRecords返回值: 0
2025-08-26 11:49:52.109642:   - 报告数量: 0
2025-08-26 11:49:52.109642: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:52.110643:   - 发现标签数量: 0
2025-08-26 11:49:52.110643:   - 未发现任何RFID标签
2025-08-26 11:49:52.110643: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:52.111643: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:52.590634: 🔄 开始RFID轮询检查...
2025-08-26 11:49:52.590634: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:52.590634: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:52.608637: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:52.609634:   - 设备句柄: 2395416852128
2025-08-26 11:49:52.609634:   - FetchRecords返回值: 0
2025-08-26 11:49:52.609634:   - 报告数量: 0
2025-08-26 11:49:52.610633: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:52.610633:   - 发现标签数量: 0
2025-08-26 11:49:52.610633:   - 未发现任何RFID标签
2025-08-26 11:49:52.610633: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:52.610633: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:53.090625: 🔄 开始RFID轮询检查...
2025-08-26 11:49:53.090625: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:53.090625: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:53.108624: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:53.108624:   - 设备句柄: 2395416852128
2025-08-26 11:49:53.109625:   - FetchRecords返回值: 0
2025-08-26 11:49:53.109625:   - 报告数量: 0
2025-08-26 11:49:53.109625: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:53.109625:   - 发现标签数量: 0
2025-08-26 11:49:53.110625:   - 未发现任何RFID标签
2025-08-26 11:49:53.110625: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:53.110625: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:53.590616: 🔄 开始RFID轮询检查...
2025-08-26 11:49:53.590616: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:53.591617: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:53.607619: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:53.608616:   - 设备句柄: 2395416852128
2025-08-26 11:49:53.609617:   - FetchRecords返回值: 0
2025-08-26 11:49:53.609617:   - 报告数量: 0
2025-08-26 11:49:53.609617: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:53.609617:   - 发现标签数量: 0
2025-08-26 11:49:53.610616:   - 未发现任何RFID标签
2025-08-26 11:49:53.610616: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:53.610616: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:54.090608: 🔄 开始RFID轮询检查...
2025-08-26 11:49:54.090608: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:54.091608: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:54.108607: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:54.109614:   - 设备句柄: 2395416852128
2025-08-26 11:49:54.110608:   - FetchRecords返回值: 0
2025-08-26 11:49:54.110608:   - 报告数量: 0
2025-08-26 11:49:54.110608: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:54.111608:   - 发现标签数量: 0
2025-08-26 11:49:54.111608:   - 未发现任何RFID标签
2025-08-26 11:49:54.111608: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:54.111608: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:54.590598: 🔄 开始RFID轮询检查...
2025-08-26 11:49:54.590598: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:54.590598: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:54.607599: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:54.608600:   - 设备句柄: 2395416852128
2025-08-26 11:49:54.608600:   - FetchRecords返回值: 0
2025-08-26 11:49:54.609599:   - 报告数量: 0
2025-08-26 11:49:54.609599: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:54.609599:   - 发现标签数量: 0
2025-08-26 11:49:54.610600:   - 未发现任何RFID标签
2025-08-26 11:49:54.610600: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:54.610600: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:55.090597: 🔄 开始RFID轮询检查...
2025-08-26 11:49:55.090597: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:55.091591: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:55.108590: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:55.108590:   - 设备句柄: 2395416852128
2025-08-26 11:49:55.109589:   - FetchRecords返回值: 0
2025-08-26 11:49:55.109589:   - 报告数量: 0
2025-08-26 11:49:55.109589: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:55.109589:   - 发现标签数量: 0
2025-08-26 11:49:55.109589:   - 未发现任何RFID标签
2025-08-26 11:49:55.110590: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:55.110590: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:55.590581: 🔄 开始RFID轮询检查...
2025-08-26 11:49:55.590581: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:55.591582: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:55.607582: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:55.607582:   - 设备句柄: 2395416852128
2025-08-26 11:49:55.608582:   - FetchRecords返回值: 0
2025-08-26 11:49:55.608582:   - 报告数量: 0
2025-08-26 11:49:55.609582: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:55.609582:   - 发现标签数量: 0
2025-08-26 11:49:55.609582:   - 未发现任何RFID标签
2025-08-26 11:49:55.609582: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:55.610582: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:56.090573: 🔄 开始RFID轮询检查...
2025-08-26 11:49:56.090573: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:56.091573: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:56.108573: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:56.109574:   - 设备句柄: 2395416852128
2025-08-26 11:49:56.109574:   - FetchRecords返回值: 0
2025-08-26 11:49:56.110574:   - 报告数量: 0
2025-08-26 11:49:56.110574: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:56.111574:   - 发现标签数量: 0
2025-08-26 11:49:56.111574:   - 未发现任何RFID标签
2025-08-26 11:49:56.112573: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:56.112573: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:56.590564: 🔄 开始RFID轮询检查...
2025-08-26 11:49:56.590564: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:56.590564: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:56.608563: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:56.608563:   - 设备句柄: 2395416852128
2025-08-26 11:49:56.608563:   - FetchRecords返回值: 0
2025-08-26 11:49:56.609564:   - 报告数量: 0
2025-08-26 11:49:56.609564: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:56.609564:   - 发现标签数量: 0
2025-08-26 11:49:56.610569:   - 未发现任何RFID标签
2025-08-26 11:49:56.610569: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:56.611564: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:57.090555: 🔄 开始RFID轮询检查...
2025-08-26 11:49:57.090555: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:57.090555: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:57.108555: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:57.109561:   - 设备句柄: 2395416852128
2025-08-26 11:49:57.109561:   - FetchRecords返回值: 0
2025-08-26 11:49:57.109561:   - 报告数量: 0
2025-08-26 11:49:57.110556: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:57.110556:   - 发现标签数量: 0
2025-08-26 11:49:57.110556:   - 未发现任何RFID标签
2025-08-26 11:49:57.111555: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:57.111555: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:57.590547: 🔄 开始RFID轮询检查...
2025-08-26 11:49:57.590547: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:57.591548: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:57.607548: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:57.608551:   - 设备句柄: 2395416852128
2025-08-26 11:49:57.609547:   - FetchRecords返回值: 0
2025-08-26 11:49:57.609547:   - 报告数量: 0
2025-08-26 11:49:57.609547: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:57.610546:   - 发现标签数量: 0
2025-08-26 11:49:57.610546:   - 未发现任何RFID标签
2025-08-26 11:49:57.610546: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:57.610546: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:58.090538: 🔄 开始RFID轮询检查...
2025-08-26 11:49:58.090538: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:58.091538: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:58.109537: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:58.110543:   - 设备句柄: 2395416852128
2025-08-26 11:49:58.110543:   - FetchRecords返回值: 0
2025-08-26 11:49:58.110543:   - 报告数量: 0
2025-08-26 11:49:58.110543: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:58.111539:   - 发现标签数量: 0
2025-08-26 11:49:58.111539:   - 未发现任何RFID标签
2025-08-26 11:49:58.111539: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:58.111539: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:58.382540: 发送心跳
2025-08-26 11:49:58.383533: Req msgType：Sip2MsgType.scStatus ,length:20， ret:  9900522.00AY3AZFC9E
2025-08-26 11:49:58.441533: Rsp : 98YYYNNN00500320250826    1149512.00AOhlsp|AM海恒图书馆|BXYYYYYYYYYYYYYYYY|AN3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AF|AG|AY3AZD51B
2025-08-26 11:49:58.590529: 🔄 开始RFID轮询检查...
2025-08-26 11:49:58.590529: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:58.590529: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:58.607529: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:58.607529:   - 设备句柄: 2395416852128
2025-08-26 11:49:58.608529:   - FetchRecords返回值: 0
2025-08-26 11:49:58.608529:   - 报告数量: 0
2025-08-26 11:49:58.608529: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:58.609530:   - 发现标签数量: 0
2025-08-26 11:49:58.609530:   - 未发现任何RFID标签
2025-08-26 11:49:58.609530: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:58.610529: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:59.090520: 🔄 开始RFID轮询检查...
2025-08-26 11:49:59.090520: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:59.090520: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:59.108520: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:59.108520:   - 设备句柄: 2395416852128
2025-08-26 11:49:59.109520:   - FetchRecords返回值: 0
2025-08-26 11:49:59.109520:   - 报告数量: 0
2025-08-26 11:49:59.109520: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:59.109520:   - 发现标签数量: 0
2025-08-26 11:49:59.109520:   - 未发现任何RFID标签
2025-08-26 11:49:59.109520: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:59.109520: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:49:59.590511: 🔄 开始RFID轮询检查...
2025-08-26 11:49:59.590511: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:49:59.590511: ⚠️ tagList为空，场上无标签
2025-08-26 11:49:59.607512: 🔍 LSGate硬件扫描详情:
2025-08-26 11:49:59.608512:   - 设备句柄: 2395416852128
2025-08-26 11:49:59.608512:   - FetchRecords返回值: 0
2025-08-26 11:49:59.609512:   - 报告数量: 0
2025-08-26 11:49:59.609512: 📊 LSGate扫描结果汇总:
2025-08-26 11:49:59.609512:   - 发现标签数量: 0
2025-08-26 11:49:59.610512:   - 未发现任何RFID标签
2025-08-26 11:49:59.610512: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:49:59.610512: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:50:00.090505: 🔄 开始RFID轮询检查...
2025-08-26 11:50:00.090505: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:50:00.091504: ⚠️ tagList为空，场上无标签
2025-08-26 11:50:00.108504: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:00.109503:   - 设备句柄: 2395416852128
2025-08-26 11:50:00.109503:   - FetchRecords返回值: 0
2025-08-26 11:50:00.109503:   - 报告数量: 0
2025-08-26 11:50:00.110503: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:00.110503:   - 发现标签数量: 0
2025-08-26 11:50:00.110503:   - 未发现任何RFID标签
2025-08-26 11:50:00.110503: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:50:00.110503: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:50:00.314500: 接收到数据: aa 00 c8 80 00 00 27 82
2025-08-26 11:50:00.314500: 🔍 接收到串口数据: aa 00 c8 80 00 00 27 82
2025-08-26 11:50:00.314500: 🔍 数据长度: 8 字节
2025-08-26 11:50:00.315499: 🔍 预定义命令列表:
2025-08-26 11:50:00.315499:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-26 11:50:00.315499:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-26 11:50:00.315499:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-26 11:50:00.315499:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-26 11:50:00.316498:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-26 11:50:00.316498:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-26 11:50:00.316498:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-26 11:50:00.316498:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-26 11:50:00.316498:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-26 11:50:00.316498:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-26 11:50:00.317498: ✅ 解析到闸机命令: GateCommand.exitStart
2025-08-26 11:50:00.317498: 解析到闸机命令: exit_start (出馆开始)
2025-08-26 11:50:00.317498: 收到闸机命令: exit_start (出馆开始)
2025-08-26 11:50:00.317498: 🚪 收到出馆开始命令，等待出馆到位信号...
2025-08-26 11:50:00.317498: 闸机状态变更: GateState.idle -> GateState.exitStarted
2025-08-26 11:50:00.317498: 闸机状态更新: GateState.idle -> GateState.exitStarted
2025-08-26 11:50:00.318498: 📊 流程状态：出馆流程已开始，等待到位信号
2025-08-26 11:50:00.318498: [channel_1] 收到闸机事件: state_changed
2025-08-26 11:50:00.319499: 📨 收到GateCoordinator事件: state_changed
2025-08-26 11:50:00.319499: 闸机状态变更: GateState.exitStarted
2025-08-26 11:50:00.319499: 🎨 处理状态变更UI: exitStarted
2025-08-26 11:50:00.319499: 未处理的状态变更UI: exitStarted
2025-08-26 11:50:00.319499: [channel_1] 收到闸机事件: exit_start
2025-08-26 11:50:00.319499: [channel_1] 主从机扩展：处理出馆开始（请求-响应模式）
2025-08-26 11:50:00.320499: 扫描结果已清空
2025-08-26 11:50:00.320499: 🧹 [channel_1] 已清空RFID服务扫描结果（页面计数重置）
2025-08-26 11:50:00.320499: 清空共享扫描池和RFID缓冲区（保持HWTagProvider标签列表）...
2025-08-26 11:50:00.320499: 🧹 开始清空共享扫描池...
2025-08-26 11:50:00.320499: 📊 清空前状态: 大小=0, 内容=[]
2025-08-26 11:50:00.320499: 🔄 重置RFID去重集合...
2025-08-26 11:50:00.320499: 🔄 开始重置已处理条码集合...
2025-08-26 11:50:00.321499: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 11:50:00.321499: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 11:50:00.321499: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 11:50:00.321499: 📊 当前tagList状态: 0个标签
2025-08-26 11:50:00.322498: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 11:50:00.322498: 🔄 开始RFID轮询检查...
2025-08-26 11:50:00.322498: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:50:00.322498: ⚠️ tagList为空，场上无标签
2025-08-26 11:50:00.322498: ✅ 已重置RFID去重集合，现有标签将被重新识别
2025-08-26 11:50:00.323499: 📡 RFID扫描状态（清空后）: isScanning=false
2025-08-26 11:50:00.323499: ⚠️ RFID未在扫描状态，尝试启动数据收集以恢复轮询...
2025-08-26 11:50:00.324499: 开始RFID数据收集...
2025-08-26 11:50:00.324499: 🔄 RFID数据收集已在进行中，重置防重复机制
2025-08-26 11:50:00.324499: ✅ 已处理条码列表已清空，轮询将重新发现标签
2025-08-26 11:50:00.324499: ✅ 共享扫描池已清空: 0 -> 0
2025-08-26 11:50:00.324499: 📡 清空通知已发送，等待RFID重新检测标签...
2025-08-26 11:50:00.325499: 清空RFID扫描缓冲区...
2025-08-26 11:50:00.325499: 🧹 已清空HWTagProvider: 0 -> 0个标签
2025-08-26 11:50:00.325499: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-26 11:50:00.325499: 🔄 开始重置已处理条码集合...
2025-08-26 11:50:00.325499: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 11:50:00.325499: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 11:50:00.325499: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 11:50:00.326499: 📊 当前tagList状态: 0个标签
2025-08-26 11:50:00.326499: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 11:50:00.326499: 🔄 开始RFID轮询检查...
2025-08-26 11:50:00.326499: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:50:00.326499: ⚠️ tagList为空，场上无标签
2025-08-26 11:50:00.326499: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-26 11:50:00.327499: 📨 收到GateCoordinator事件: exit_start
2025-08-26 11:50:00.327499: 页面状态变更: SilencePageState.waitingExit
2025-08-26 11:50:00.327499: RFID数据收集已启动
2025-08-26 11:50:00.327499: ✅ 已启动RFID数据收集（恢复轮询）
2025-08-26 11:50:00.328499: 🔄 开始重置已处理条码集合...
2025-08-26 11:50:00.328499: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 11:50:00.328499: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 11:50:00.328499: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 11:50:00.328499: 📊 当前tagList状态: 0个标签
2025-08-26 11:50:00.328499: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 11:50:00.328499: 🔄 开始RFID轮询检查...
2025-08-26 11:50:00.329498: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:50:00.329498: ⚠️ tagList为空，场上无标签
2025-08-26 11:50:00.329498: 共享扫描池和RFID缓冲区已清空，已处理条码集合已重置
2025-08-26 11:50:00.329498: 🧹 [channel_1] 主机清空列表1和RFID缓冲区: 清除0个条码
2025-08-26 11:50:00.591494: 🔄 开始RFID轮询检查...
2025-08-26 11:50:00.591494: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:50:00.591494: ⚠️ tagList为空，场上无标签
2025-08-26 11:50:00.608494: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:00.608494:   - 设备句柄: 2395416852128
2025-08-26 11:50:00.609497:   - FetchRecords返回值: 0
2025-08-26 11:50:00.609497:   - 报告数量: 0
2025-08-26 11:50:00.609497: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:00.610495:   - 发现标签数量: 0
2025-08-26 11:50:00.610495:   - 未发现任何RFID标签
2025-08-26 11:50:00.610495: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:50:00.610495: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:50:01.090485: 🔄 开始RFID轮询检查...
2025-08-26 11:50:01.090485: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:50:01.090485: ⚠️ tagList为空，场上无标签
2025-08-26 11:50:01.107485: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:01.107485:   - 设备句柄: 2395416852128
2025-08-26 11:50:01.107485:   - FetchRecords返回值: 0
2025-08-26 11:50:01.108485:   - 报告数量: 0
2025-08-26 11:50:01.108485: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:01.108485:   - 发现标签数量: 0
2025-08-26 11:50:01.108485:   - 未发现任何RFID标签
2025-08-26 11:50:01.108485: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:50:01.108485: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:50:01.329483: 📊 [channel_1] 主机返回当前数据: 0个条码
2025-08-26 11:50:01.590477: 🔄 开始RFID轮询检查...
2025-08-26 11:50:01.590477: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:50:01.590477: ⚠️ tagList为空，场上无标签
2025-08-26 11:50:01.608476: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:01.608476:   - 设备句柄: 2395416852128
2025-08-26 11:50:01.609476:   - FetchRecords返回值: 0
2025-08-26 11:50:01.609476:   - 报告数量: 0
2025-08-26 11:50:01.609476: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:01.609476:   - 发现标签数量: 0
2025-08-26 11:50:01.609476:   - 未发现任何RFID标签
2025-08-26 11:50:01.610476: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:50:01.610476: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:50:01.953479: 接收到数据: aa 00 0a 80 00 00 1a 3a
2025-08-26 11:50:01.954471: 🔍 接收到串口数据: aa 00 0a 80 00 00 1a 3a
2025-08-26 11:50:01.954471: 🔍 数据长度: 8 字节
2025-08-26 11:50:01.954471: 🔍 预定义命令列表:
2025-08-26 11:50:01.955470:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-26 11:50:01.955470:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-26 11:50:01.955470:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-26 11:50:01.955470:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-26 11:50:01.955470:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-26 11:50:01.955470:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-26 11:50:01.955470:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-26 11:50:01.956470:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-26 11:50:01.956470:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-26 11:50:01.956470:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-26 11:50:01.956470: ✅ 解析到闸机命令: GateCommand.reachPosition
2025-08-26 11:50:01.957472: 解析到闸机命令: position_reached (到达指定位置)
2025-08-26 11:50:01.959471: 收到闸机命令: position_reached (到达指定位置)
2025-08-26 11:50:01.960476: 📍 收到到位信号，当前状态: GateState.exitStarted
2025-08-26 11:50:01.960476: 📊 流程状态：进馆=false, 出馆=true
2025-08-26 11:50:01.960476: 📊 待处理认证：进馆=false, 出馆=false
2025-08-26 11:50:01.960476: 🚪 出馆到位信号，启动认证和10秒数据收集...
2025-08-26 11:50:01.961471: 闸机状态变更: GateState.exitStarted -> GateState.exitWaitingAuth
2025-08-26 11:50:01.961471: 闸机状态更新: GateState.exitStarted -> GateState.exitWaitingAuth
2025-08-26 11:50:01.962474: 🔐 启动出馆认证系统（不关注结果）...
2025-08-26 11:50:01.962474: 闸机状态变更: GateState.exitWaitingAuth -> GateState.exitScanning
2025-08-26 11:50:01.962474: 闸机状态更新: GateState.exitWaitingAuth -> GateState.exitScanning
2025-08-26 11:50:01.962474: 多认证管理器状态变更: listening
2025-08-26 11:50:01.963470: 启动所有认证方式监听: [AuthMethod.readerCard]
2025-08-26 11:50:01.963470: 准备启动 1 个物理认证服务
2025-08-26 11:50:01.963470: 开始读卡器认证监听
2025-08-26 11:50:01.963470: 🔥 测试：跳过强制重新配置，保持现有连接
2025-08-26 11:50:01.963470: 已移除读卡器状态监听器
2025-08-26 11:50:01.964470: 已移除标签数据监听器
2025-08-26 11:50:01.964470: 所有卡片监听器已移除
2025-08-26 11:50:01.964470: 已添加读卡器状态监听器
2025-08-26 11:50:01.964470: 已添加标签数据监听器
2025-08-26 11:50:01.965470: 开始监听卡片数据 - 所有监听器已就绪
2025-08-26 11:50:01.965470: 读卡器认证监听启动成功
2025-08-26 11:50:01.965470: ✅ 出馆认证系统已启动
2025-08-26 11:50:01.965470: 🚀 启动出馆10秒数据收集...
2025-08-26 11:50:01.966470: 🔧 启动10秒计时器，当前时间: 2025-08-26 11:50:01.958471
2025-08-26 11:50:01.966470: 🔧 10秒计时器已设置，计时器对象: Instance of '_Timer'
2025-08-26 11:50:01.966470: 📡 开始从共享池收集数据...
2025-08-26 11:50:01.966470: 🔧 RFID扫描已在运行，只清空共享池准备收集新数据
2025-08-26 11:50:01.966470: 清空共享扫描池和RFID缓冲区（保持HWTagProvider标签列表）...
2025-08-26 11:50:01.966470: 🧹 开始清空共享扫描池...
2025-08-26 11:50:01.967470: 📊 清空前状态: 大小=0, 内容=[]
2025-08-26 11:50:01.967470: 🔄 重置RFID去重集合...
2025-08-26 11:50:01.967470: 🔄 开始重置已处理条码集合...
2025-08-26 11:50:01.967470: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 11:50:01.967470: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 11:50:01.967470: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 11:50:01.968470: 📊 当前tagList状态: 0个标签
2025-08-26 11:50:01.968470: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 11:50:01.968470: 🔄 开始RFID轮询检查...
2025-08-26 11:50:01.968470: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:50:01.968470: ⚠️ tagList为空，场上无标签
2025-08-26 11:50:01.968470: ✅ 已重置RFID去重集合，现有标签将被重新识别
2025-08-26 11:50:01.968470: 📡 RFID扫描状态（清空后）: isScanning=true
2025-08-26 11:50:01.969470: ✅ 共享扫描池已清空: 0 -> 0
2025-08-26 11:50:01.969470: 📡 清空通知已发送，等待RFID重新检测标签...
2025-08-26 11:50:01.969470: 清空RFID扫描缓冲区...
2025-08-26 11:50:01.969470: 🧹 已清空HWTagProvider: 0 -> 0个标签
2025-08-26 11:50:01.969470: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-26 11:50:01.970470: 🔄 开始重置已处理条码集合...
2025-08-26 11:50:01.970470: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 11:50:01.970470: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 11:50:01.970470: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 11:50:01.970470: 📊 当前tagList状态: 0个标签
2025-08-26 11:50:01.970470: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 11:50:01.970470: 🔄 开始RFID轮询检查...
2025-08-26 11:50:01.970470: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:50:01.970470: ⚠️ tagList为空，场上无标签
2025-08-26 11:50:01.971470: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-26 11:50:01.971470: [channel_1] 收到闸机事件: state_changed
2025-08-26 11:50:01.971470: 📨 收到GateCoordinator事件: state_changed
2025-08-26 11:50:01.972471: 闸机状态变更: GateState.exitWaitingAuth
2025-08-26 11:50:01.972471: 🎨 处理状态变更UI: exitWaitingAuth
2025-08-26 11:50:01.972471: 未处理的状态变更UI: exitWaitingAuth
2025-08-26 11:50:01.973471: 读者证 认证服务启动成功
2025-08-26 11:50:01.973471: 所有认证服务启动完成，成功启动 1 个服务
2025-08-26 11:50:01.974473: 当前可用的认证方式: 读者证
2025-08-26 11:50:01.974473: 🔄 开始重置已处理条码集合...
2025-08-26 11:50:01.974473: 📊 重置前状态: 大小=0, 内容=[]
2025-08-26 11:50:01.975470: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-26 11:50:01.975470: 🔄 当前场上标签将被重新识别为新标签
2025-08-26 11:50:01.975470: 📊 当前tagList状态: 0个标签
2025-08-26 11:50:01.975470: 🚀 立即触发轮询，加速标签重新发现...
2025-08-26 11:50:01.975470: 🔄 开始RFID轮询检查...
2025-08-26 11:50:01.975470: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:50:01.976470: ⚠️ tagList为空，场上无标签
2025-08-26 11:50:01.976470: 共享扫描池和RFID缓冲区已清空，已处理条码集合已重置
2025-08-26 11:50:01.976470: [channel_1] 收到闸机事件: state_changed
2025-08-26 11:50:01.976470: 📨 收到GateCoordinator事件: state_changed
2025-08-26 11:50:01.976470: 闸机状态变更: GateState.exitScanning
2025-08-26 11:50:01.976470: 🎨 处理状态变更UI: exitScanning
2025-08-26 11:50:01.976470: 页面状态变更: SilencePageState.rfidScanning
2025-08-26 11:50:01.977470: 🧪 模拟模式：检测到测试卡片（调用次数: 1）
2025-08-26 11:50:01.977470: 🧪 模拟模式 - 使用测试patron: 2017621493 (索引: 0, 总调用次数: 1)
2025-08-26 11:50:01.978470: 🧪 模拟检测到卡片: 2017621493
2025-08-26 11:50:01.978470: 读卡器数据认证：
2025-08-26 11:50:01.978470:   设备类型: 10
2025-08-26 11:50:01.978470:   条码: 2017621493
2025-08-26 11:50:01.978470:   标签UID: SIM2017621493
2025-08-26 11:50:01.978470:   对应登录类型: AuthLoginType.readerCard
2025-08-26 11:50:01.978470:   根据读卡器类型10确定认证方式为: 读者证
2025-08-26 11:50:01.978470:   开始调用认证API: 2017621493
2025-08-26 11:50:01.979470: 正在认证用户: 2017621493, 方式: 读者证
2025-08-26 11:50:01.979470: 多认证管理器: 读者证获得认证请求锁
2025-08-26 11:50:01.979470: 使用门径认证接口: identifier=2017621493, method=AuthMethod.readerCard, isEnter=false
2025-08-26 11:50:01.979470: 开始门径认证: identifier=2017621493, method=AuthMethod.readerCard, isEnter=false
2025-08-26 11:50:01.979470: 发送认证请求: {deviceMac: FFFFFFFF, patronSn: 2017621493, cardSn: null, type: 2}
2025-08-26 11:50:01.979470: 设备API服务初始化完成: http://***************:9000
2025-08-26 11:50:01.979470: 发送读者认证请求: /tunano/ldc/entrance/v1/api/door/verify
2025-08-26 11:50:01.980469: 请求数据: {"deviceMac":"FFFFFFFF","patronSn":"2017621493","cardSn":null,"type":2}
2025-08-26 11:50:02.002469: 读者认证响应: {errorCode: 0, message: 验证通过, data: null}
2025-08-26 11:50:02.003469: 认证响应: errorCode=0, message=验证通过
2025-08-26 11:50:02.003469: 认证成功，跳过用户信息获取
2025-08-26 11:50:02.003469: 门径认证结果: AuthStatus.success, 用户=认证用户
2025-08-26 11:50:02.003469:   认证结果: AuthStatus.success, 方式: 读者证
2025-08-26 11:50:02.004469: 认证结果已产生，立即停止读卡器扫描
2025-08-26 11:50:02.004469: 没有活跃的读卡器连接需要停止
2025-08-26 11:50:02.004469: 多认证管理器: 收到认证结果，立即停止所有读卡器扫描
2025-08-26 11:50:02.005470: 立即停止所有读卡器扫描
2025-08-26 11:50:02.005470: 停止读卡器认证监听
2025-08-26 11:50:02.005470: 多认证管理器: 收到认证结果: 读者证 - success
2025-08-26 11:50:02.005470: 多认证管理器状态变更: authenticating
2025-08-26 11:50:02.005470: 多认证管理器: 认证成功，保持其他认证方式继续监听
2025-08-26 11:50:02.005470: 多认证管理器状态变更: completed
2025-08-26 11:50:02.005470: 多认证管理器: 认证结果中没有读者信息，跳过认证后处理
2025-08-26 11:50:02.006469: 多认证管理器: 认证请求锁将在5秒后释放
2025-08-26 11:50:02.006469: 已移除读卡器状态监听器
2025-08-26 11:50:02.006469: 已移除标签数据监听器
2025-08-26 11:50:02.006469: 所有卡片监听器已移除
2025-08-26 11:50:02.006469: 没有活跃的读卡器连接需要暂停
2025-08-26 11:50:02.006469: 📱 收到出馆认证结果: AuthStatus.success, 用户: 认证用户
2025-08-26 11:50:02.006469: ✅ 出馆认证API请求已发送，继续数据收集流程（不关注认证结果）
2025-08-26 11:50:02.006469: 读卡器认证监听已停止（连接保持）
2025-08-26 11:50:02.007469: 已停止 读者证 扫描
2025-08-26 11:50:02.007469: [channel_1] 收到闸机事件: exit_auth_success
2025-08-26 11:50:02.007469: 📨 收到GateCoordinator事件: exit_auth_success
2025-08-26 11:50:02.007469: 未处理的GateCoordinator事件: exit_auth_success
2025-08-26 11:50:02.089471: 🔄 开始RFID轮询检查...
2025-08-26 11:50:02.089471: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:50:02.090468: ⚠️ tagList为空，场上无标签
2025-08-26 11:50:02.108468: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:02.109468:   - 设备句柄: 2395416852128
2025-08-26 11:50:02.109468:   - FetchRecords返回值: 0
2025-08-26 11:50:02.109468:   - 报告数量: 0
2025-08-26 11:50:02.110471: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:02.110471:   - 发现标签数量: 0
2025-08-26 11:50:02.110471:   - 未发现任何RFID标签
2025-08-26 11:50:02.111468: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:50:02.111468: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:50:02.590459: 🔄 开始RFID轮询检查...
2025-08-26 11:50:02.590459: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:50:02.590459: ⚠️ tagList为空，场上无标签
2025-08-26 11:50:02.608470: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:02.609459:   - 设备句柄: 2395416852128
2025-08-26 11:50:02.609459:   - FetchRecords返回值: 0
2025-08-26 11:50:02.609459:   - 报告数量: 0
2025-08-26 11:50:02.610459: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:02.610459:   - 发现标签数量: 0
2025-08-26 11:50:02.610459:   - 未发现任何RFID标签
2025-08-26 11:50:02.610459: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:50:02.610459: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:50:03.090450: 🔄 开始RFID轮询检查...
2025-08-26 11:50:03.090450: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:50:03.090450: ⚠️ tagList为空，场上无标签
2025-08-26 11:50:03.110452: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:03.110452:   - 设备句柄: 2395416852128
2025-08-26 11:50:03.110452:   - FetchRecords返回值: 0
2025-08-26 11:50:03.111451:   - 报告数量: 0
2025-08-26 11:50:03.111451: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:03.112452:   - 发现标签数量: 0
2025-08-26 11:50:03.112452:   - 未发现任何RFID标签
2025-08-26 11:50:03.112452: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:50:03.112452: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:50:03.590442: 🔄 开始RFID轮询检查...
2025-08-26 11:50:03.591444: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:50:03.591444: ⚠️ tagList为空，场上无标签
2025-08-26 11:50:03.608442: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:03.609442:   - 设备句柄: 2395416852128
2025-08-26 11:50:03.609442:   - FetchRecords返回值: 0
2025-08-26 11:50:03.610443:   - 报告数量: 0
2025-08-26 11:50:03.610443: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:03.610443:   - 发现标签数量: 0
2025-08-26 11:50:03.610443:   - 未发现任何RFID标签
2025-08-26 11:50:03.611442: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:50:03.611442: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:50:04.091433: 🔄 开始RFID轮询检查...
2025-08-26 11:50:04.091433: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:50:04.091433: ⚠️ tagList为空，场上无标签
2025-08-26 11:50:04.107433: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:04.107433:   - 设备句柄: 2395416852128
2025-08-26 11:50:04.108433:   - FetchRecords返回值: 0
2025-08-26 11:50:04.108433:   - 报告数量: 0
2025-08-26 11:50:04.108433: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:04.108433:   - 发现标签数量: 0
2025-08-26 11:50:04.108433:   - 未发现任何RFID标签
2025-08-26 11:50:04.108433: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:50:04.109433: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:50:04.590424: 🔄 开始RFID轮询检查...
2025-08-26 11:50:04.590424: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:50:04.591426: ⚠️ tagList为空，场上无标签
2025-08-26 11:50:04.608425: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:04.608425:   - 设备句柄: 2395416852128
2025-08-26 11:50:04.609425:   - FetchRecords返回值: 0
2025-08-26 11:50:04.609425:   - 报告数量: 0
2025-08-26 11:50:04.610426: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:04.610426:   - 发现标签数量: 0
2025-08-26 11:50:04.610426:   - 未发现任何RFID标签
2025-08-26 11:50:04.610426: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:50:04.611426: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:50:05.090416: 🔄 开始RFID轮询检查...
2025-08-26 11:50:05.090416: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:50:05.090416: ⚠️ tagList为空，场上无标签
2025-08-26 11:50:05.107415: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:05.108416:   - 设备句柄: 2395416852128
2025-08-26 11:50:05.108416:   - FetchRecords返回值: 0
2025-08-26 11:50:05.108416:   - 报告数量: 0
2025-08-26 11:50:05.108416: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:05.109416:   - 发现标签数量: 0
2025-08-26 11:50:05.109416:   - 未发现任何RFID标签
2025-08-26 11:50:05.109416: 🔍 LSGate ReaderManager收到扫描结果: 0 个UID
2025-08-26 11:50:05.109416: 🚫 LSGate未检测到任何RFID标签
2025-08-26 11:50:05.590407: 🔄 开始RFID轮询检查...
2025-08-26 11:50:05.590407: 📊 轮询状态: 扫描中=true, tagList=0个标签, 已处理=0个
2025-08-26 11:50:05.590407: ⚠️ tagList为空，场上无标签
2025-08-26 11:50:05.609407: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:05.610407:   - 设备句柄: 2395416852128
2025-08-26 11:50:05.610407:   - FetchRecords返回值: 0
2025-08-26 11:50:05.610407:   - 报告数量: 1
2025-08-26 11:50:05.610407:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:05.610407:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:05.611407:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:05.611407:   - 设备类型: LSGControlCenter
2025-08-26 11:50:05.611407:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:05.611407:   - 数据长度: 8
2025-08-26 11:50:05.611407:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:05.611407:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:05.611407: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:05.611407:   - 发现标签数量: 1
2025-08-26 11:50:05.612406:   - 标签详情:
2025-08-26 11:50:05.612406:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:05.612406: RFID扫描: 发现 1 个标签
2025-08-26 11:50:05.612406: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 11:50:05.612406: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:05.612406: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:05.612406:   - UID: E004015305F83C8E
2025-08-26 11:50:05.612406:   - Data: E004015305F83C8E
2025-08-26 11:50:05.613407:   - EventType: 1
2025-08-26 11:50:05.613407:   - Direction: 0
2025-08-26 11:50:05.613407:   - Antenna: 1
2025-08-26 11:50:05.613407:   - TagFrequency: 0
2025-08-26 11:50:05.613407: data:E004015305F83C8E,coder:Coder15962Std
2025-08-26 11:50:05.613407: parseRet：{"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]},decoder:15962标准协议
2025-08-26 11:50:05.613407: 🎯 HWTagProvider.changeAddedItem: 收到 1 个新标签
2025-08-26 11:50:05.614406: 🏷️ 新标签[0]: uid=E004015305F83C8E, barCode=null, readerType=22
2025-08-26 11:50:05.614406: 📡 LSGate标签详情: {uid: E004015305F83C8E, barCode: null, eas: null, tagType: null, libraryCode: null, afiStr: null, afiData: null, readerType: 22, decoderType: 15962标准协议, dsfID: null, oidList: [{oid: 0, compressMode: 110, data: S, originHexStr: E004015300000000, originData: [53], isKeepInTag: true}], data: E004015305F83C8E, leftBinary: null, sortType: null, codeType: null, version: null, contentIndex: null, tagFrequency: 0, info: null, inAnt: 1}
2025-08-26 11:50:05.614406: 🎯 HWTagProvider: 当前总标签数 1, 新增标签数 1
2025-08-26 11:50:06.091398: 🔄 开始RFID轮询检查...
2025-08-26 11:50:06.091398: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=0个
2025-08-26 11:50:06.091398: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:06.092398: 🆕 轮询发现新标签(UID作为条码): E004015305F83C8E
2025-08-26 11:50:06.092398: 📋 添加到已处理列表: 0 -> 1
2025-08-26 11:50:06.092398: 🏷️ 检测到标签: E004015305F83C8E
2025-08-26 11:50:06.092398: 📊 当前扫描状态: 扫描中=true, 已扫描=1个
2025-08-26 11:50:06.092398: 📋 已扫描列表: [E004015305F83C8E]
2025-08-26 11:50:06.092398: ✅ 条码已发送到barcodeStream，将进入共享池: E004015305F83C8E
2025-08-26 11:50:06.092398: 📡 barcodeStream监听器数量: 有监听器
2025-08-26 11:50:06.092398: ✅ 轮询完成: 发现1个新标签，总计已处理1个标签
2025-08-26 11:50:06.092398: 📋 当前已处理标签列表: [E004015305F83C8E]
2025-08-26 11:50:06.093398: 🔄 尝试添加条码到共享池: E004015305F83C8E
2025-08-26 11:50:06.093398: 📊 添加前状态: 共享池大小=0, 是否为空=true
2025-08-26 11:50:06.093398: ✅ 成功添加条码到共享池: E004015305F83C8E (总计: 1)
2025-08-26 11:50:06.093398: 📋 当前共享池内容: [E004015305F83C8E]
2025-08-26 11:50:06.093398: 📡 共享池变化通知已发送
2025-08-26 11:50:06.093398: 🔄 尝试添加条码到共享池: E004015305F83C8E
2025-08-26 11:50:06.093398: 📊 添加前状态: 共享池大小=1, 是否为空=false
2025-08-26 11:50:06.094398: 🔄 条码已存在于共享池: E004015305F83C8E (总计: 1)
2025-08-26 11:50:06.094398: 扫描到新条码: E004015305F83C8E (总计: 1)
2025-08-26 11:50:06.094398: 页面状态变更: SilencePageState.rfidScanning
2025-08-26 11:50:06.110398: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:06.111398:   - 设备句柄: 2395416852128
2025-08-26 11:50:06.111398:   - FetchRecords返回值: 0
2025-08-26 11:50:06.111398:   - 报告数量: 1
2025-08-26 11:50:06.112398:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:06.112398:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:06.112398:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:06.112398:   - 设备类型: LSGControlCenter
2025-08-26 11:50:06.113400:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:06.113400:   - 数据长度: 8
2025-08-26 11:50:06.113400:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:06.113400:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:06.113400: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:06.113400:   - 发现标签数量: 1
2025-08-26 11:50:06.114398:   - 标签详情:
2025-08-26 11:50:06.114398:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:06.114398: RFID扫描: 发现 1 个标签
2025-08-26 11:50:06.114398: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 11:50:06.115401: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:06.115401: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:06.115401:   - UID: E004015305F83C8E
2025-08-26 11:50:06.115401:   - Data: E004015305F83C8E
2025-08-26 11:50:06.116398:   - EventType: 1
2025-08-26 11:50:06.116398:   - Direction: 0
2025-08-26 11:50:06.116398:   - Antenna: 1
2025-08-26 11:50:06.116398:   - TagFrequency: 0
2025-08-26 11:50:06.590390: 🔄 开始RFID轮询检查...
2025-08-26 11:50:06.590390: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:06.590390: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:06.591390: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:06.591390: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:06.609389: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:06.610390:   - 设备句柄: 2395416852128
2025-08-26 11:50:06.610390:   - FetchRecords返回值: 0
2025-08-26 11:50:06.610390:   - 报告数量: 1
2025-08-26 11:50:06.610390:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:06.610390:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:06.611389:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:06.611389:   - 设备类型: LSGControlCenter
2025-08-26 11:50:06.611389:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:06.611389:   - 数据长度: 8
2025-08-26 11:50:06.611389:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:06.611389:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:06.611389: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:06.612389:   - 发现标签数量: 1
2025-08-26 11:50:06.612389:   - 标签详情:
2025-08-26 11:50:06.612389:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:06.612389: RFID扫描: 发现 1 个标签
2025-08-26 11:50:06.612389: 🔍 LSGate ReaderManager收到扫描结果: 1 个UID
2025-08-26 11:50:06.612389: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:06.612389: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:06.613389:   - UID: E004015305F83C8E
2025-08-26 11:50:06.613389:   - Data: E004015305F83C8E
2025-08-26 11:50:06.613389:   - EventType: 1
2025-08-26 11:50:06.613389:   - Direction: 0
2025-08-26 11:50:06.613389:   - Antenna: 1
2025-08-26 11:50:06.613389:   - TagFrequency: 0
2025-08-26 11:50:07.008382: 多认证管理器: 认证请求锁已释放（之前为读者证）
2025-08-26 11:50:07.091380: 🔄 开始RFID轮询检查...
2025-08-26 11:50:07.091380: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:07.091380: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:07.091380: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:07.092381: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:07.109380: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:07.109380:   - 设备句柄: 2395416852128
2025-08-26 11:50:07.110388:   - FetchRecords返回值: 0
2025-08-26 11:50:07.110388:   - 报告数量: 2
2025-08-26 11:50:07.111381:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:07.111381:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:07.111381:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:07.111381:   - 设备类型: LSGControlCenter
2025-08-26 11:50:07.111381:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:07.112381:   - 数据长度: 8
2025-08-26 11:50:07.112381:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:07.112381:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:07.112381:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:07.112381:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:07.112381:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:07.113380:   - 设备类型: LSGControlCenter
2025-08-26 11:50:07.113380:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:07.113380:   - 数据长度: 8
2025-08-26 11:50:07.113380:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:07.113380:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:07.113380: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:07.113380:   - 发现标签数量: 2
2025-08-26 11:50:07.114380:   - 标签详情:
2025-08-26 11:50:07.114380:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:07.114380:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:07.114380: RFID扫描: 发现 2 个标签
2025-08-26 11:50:07.114380: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 11:50:07.114380: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:07.114380: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:07.114380:   - UID: E004015305F83C8E
2025-08-26 11:50:07.115380:   - Data: E004015305F83C8E
2025-08-26 11:50:07.115380:   - EventType: 1
2025-08-26 11:50:07.115380:   - Direction: 0
2025-08-26 11:50:07.115380:   - Antenna: 1
2025-08-26 11:50:07.115380:   - TagFrequency: 0
2025-08-26 11:50:07.115380: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:07.115380:   - UID: E004015305F83C8E
2025-08-26 11:50:07.116380:   - Data: E004015305F83C8E
2025-08-26 11:50:07.116380:   - EventType: 1
2025-08-26 11:50:07.116380:   - Direction: 0
2025-08-26 11:50:07.116380:   - Antenna: 1
2025-08-26 11:50:07.116380:   - TagFrequency: 0
2025-08-26 11:50:07.590372: 🔄 开始RFID轮询检查...
2025-08-26 11:50:07.590372: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:07.590372: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:07.590372: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:07.590372: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:07.609372: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:07.610375:   - 设备句柄: 2395416852128
2025-08-26 11:50:07.610375:   - FetchRecords返回值: 0
2025-08-26 11:50:07.610375:   - 报告数量: 2
2025-08-26 11:50:07.611373:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:07.611373:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:07.611373:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:07.611373:   - 设备类型: LSGControlCenter
2025-08-26 11:50:07.611373:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:07.612373:   - 数据长度: 8
2025-08-26 11:50:07.612373:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:07.612373:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:07.612373:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:07.613376:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:07.613376:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:07.613376:   - 设备类型: LSGControlCenter
2025-08-26 11:50:07.614372:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:07.614372:   - 数据长度: 8
2025-08-26 11:50:07.614372:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:07.614372:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:07.614372: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:07.614372:   - 发现标签数量: 2
2025-08-26 11:50:07.614372:   - 标签详情:
2025-08-26 11:50:07.615372:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:07.615372:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:07.615372: RFID扫描: 发现 2 个标签
2025-08-26 11:50:07.615372: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 11:50:07.616372: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:07.616372: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:07.616372:   - UID: E004015305F83C8E
2025-08-26 11:50:07.616372:   - Data: E004015305F83C8E
2025-08-26 11:50:07.617373:   - EventType: 1
2025-08-26 11:50:07.617373:   - Direction: 0
2025-08-26 11:50:07.617373:   - Antenna: 1
2025-08-26 11:50:07.617373:   - TagFrequency: 0
2025-08-26 11:50:07.617373: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:07.618372:   - UID: E004015305F83C8E
2025-08-26 11:50:07.618372:   - Data: E004015305F83C8E
2025-08-26 11:50:07.618372:   - EventType: 1
2025-08-26 11:50:07.618372:   - Direction: 0
2025-08-26 11:50:07.618372:   - Antenna: 1
2025-08-26 11:50:07.618372:   - TagFrequency: 0
2025-08-26 11:50:08.090364: 🔄 开始RFID轮询检查...
2025-08-26 11:50:08.090364: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:08.090364: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:08.091364: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:08.091364: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:08.109363: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:08.109363:   - 设备句柄: 2395416852128
2025-08-26 11:50:08.109363:   - FetchRecords返回值: 0
2025-08-26 11:50:08.110364:   - 报告数量: 2
2025-08-26 11:50:08.110364:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:08.110364:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:08.110364:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:08.110364:   - 设备类型: LSGControlCenter
2025-08-26 11:50:08.110364:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:08.111363:   - 数据长度: 8
2025-08-26 11:50:08.111363:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:08.111363:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:08.111363:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:08.111363:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:08.111363:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:08.111363:   - 设备类型: LSGControlCenter
2025-08-26 11:50:08.111363:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:08.112363:   - 数据长度: 8
2025-08-26 11:50:08.112363:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:08.112363:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:08.112363: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:08.112363:   - 发现标签数量: 2
2025-08-26 11:50:08.112363:   - 标签详情:
2025-08-26 11:50:08.112363:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:08.113363:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:08.113363: RFID扫描: 发现 2 个标签
2025-08-26 11:50:08.113363: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 11:50:08.113363: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:08.113363: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:08.113363:   - UID: E004015305F83C8E
2025-08-26 11:50:08.113363:   - Data: E004015305F83C8E
2025-08-26 11:50:08.113363:   - EventType: 1
2025-08-26 11:50:08.114363:   - Direction: 0
2025-08-26 11:50:08.114363:   - Antenna: 1
2025-08-26 11:50:08.114363:   - TagFrequency: 0
2025-08-26 11:50:08.114363: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:08.114363:   - UID: E004015305F83C8E
2025-08-26 11:50:08.114363:   - Data: E004015305F83C8E
2025-08-26 11:50:08.114363:   - EventType: 1
2025-08-26 11:50:08.114363:   - Direction: 0
2025-08-26 11:50:08.115363:   - Antenna: 1
2025-08-26 11:50:08.115363:   - TagFrequency: 0
2025-08-26 11:50:08.591355: 🔄 开始RFID轮询检查...
2025-08-26 11:50:08.591355: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:08.591355: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:08.592354: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:08.592354: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:08.609354: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:08.609354:   - 设备句柄: 2395416852128
2025-08-26 11:50:08.610354:   - FetchRecords返回值: 0
2025-08-26 11:50:08.610354:   - 报告数量: 2
2025-08-26 11:50:08.610354:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:08.610354:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:08.610354:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:08.611354:   - 设备类型: LSGControlCenter
2025-08-26 11:50:08.611354:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:08.611354:   - 数据长度: 8
2025-08-26 11:50:08.611354:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:08.611354:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:08.611354:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:08.611354:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:08.612354:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:08.612354:   - 设备类型: LSGControlCenter
2025-08-26 11:50:08.612354:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:08.612354:   - 数据长度: 8
2025-08-26 11:50:08.612354:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:08.612354:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:08.612354: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:08.613354:   - 发现标签数量: 2
2025-08-26 11:50:08.613354:   - 标签详情:
2025-08-26 11:50:08.613354:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:08.613354:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:08.613354: RFID扫描: 发现 2 个标签
2025-08-26 11:50:08.613354: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 11:50:08.613354: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:08.613354: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:08.614354:   - UID: E004015305F83C8E
2025-08-26 11:50:08.614354:   - Data: E004015305F83C8E
2025-08-26 11:50:08.614354:   - EventType: 1
2025-08-26 11:50:08.614354:   - Direction: 0
2025-08-26 11:50:08.614354:   - Antenna: 1
2025-08-26 11:50:08.614354:   - TagFrequency: 0
2025-08-26 11:50:08.614354: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:08.614354:   - UID: E004015305F83C8E
2025-08-26 11:50:08.615354:   - Data: E004015305F83C8E
2025-08-26 11:50:08.615354:   - EventType: 1
2025-08-26 11:50:08.615354:   - Direction: 0
2025-08-26 11:50:08.615354:   - Antenna: 1
2025-08-26 11:50:08.615354:   - TagFrequency: 0
2025-08-26 11:50:09.090345: 🔄 开始RFID轮询检查...
2025-08-26 11:50:09.090345: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:09.090345: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:09.090345: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:09.090345: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:09.109346: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:09.109346:   - 设备句柄: 2395416852128
2025-08-26 11:50:09.110347:   - FetchRecords返回值: 0
2025-08-26 11:50:09.110347:   - 报告数量: 2
2025-08-26 11:50:09.110347:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:09.111350:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:09.111350:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:09.111350:   - 设备类型: LSGControlCenter
2025-08-26 11:50:09.111350:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:09.112347:   - 数据长度: 8
2025-08-26 11:50:09.112347:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:09.112347:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:09.112347:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:09.112347:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:09.112347:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:09.113346:   - 设备类型: LSGControlCenter
2025-08-26 11:50:09.113346:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:09.113346:   - 数据长度: 8
2025-08-26 11:50:09.113346:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:09.114349:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:09.114349: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:09.114349:   - 发现标签数量: 2
2025-08-26 11:50:09.114349:   - 标签详情:
2025-08-26 11:50:09.115346:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:09.115346:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:09.115346: RFID扫描: 发现 2 个标签
2025-08-26 11:50:09.115346: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 11:50:09.115346: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:09.116346: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:09.116346:   - UID: E004015305F83C8E
2025-08-26 11:50:09.116346:   - Data: E004015305F83C8E
2025-08-26 11:50:09.116346:   - EventType: 1
2025-08-26 11:50:09.116346:   - Direction: 0
2025-08-26 11:50:09.116346:   - Antenna: 1
2025-08-26 11:50:09.116346:   - TagFrequency: 0
2025-08-26 11:50:09.117345: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:09.117345:   - UID: E004015305F83C8E
2025-08-26 11:50:09.117345:   - Data: E004015305F83C8E
2025-08-26 11:50:09.117345:   - EventType: 1
2025-08-26 11:50:09.117345:   - Direction: 0
2025-08-26 11:50:09.117345:   - Antenna: 1
2025-08-26 11:50:09.117345:   - TagFrequency: 0
2025-08-26 11:50:09.590340: 🔄 开始RFID轮询检查...
2025-08-26 11:50:09.590340: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:09.591338: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:09.591338: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:09.591338: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:09.609337: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:09.609337:   - 设备句柄: 2395416852128
2025-08-26 11:50:09.609337:   - FetchRecords返回值: 0
2025-08-26 11:50:09.610337:   - 报告数量: 2
2025-08-26 11:50:09.610337:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:09.610337:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:09.610337:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:09.610337:   - 设备类型: LSGControlCenter
2025-08-26 11:50:09.610337:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:09.610337:   - 数据长度: 8
2025-08-26 11:50:09.610337:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:09.611337:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:09.611337:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:09.611337:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:09.611337:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:09.611337:   - 设备类型: LSGControlCenter
2025-08-26 11:50:09.611337:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:09.612338:   - 数据长度: 8
2025-08-26 11:50:09.612338:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:09.612338:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:09.612338: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:09.613337:   - 发现标签数量: 2
2025-08-26 11:50:09.613337:   - 标签详情:
2025-08-26 11:50:09.613337:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:09.613337:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:09.613337: RFID扫描: 发现 2 个标签
2025-08-26 11:50:09.614337: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 11:50:09.614337: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:09.614337: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:09.614337:   - UID: E004015305F83C8E
2025-08-26 11:50:09.614337:   - Data: E004015305F83C8E
2025-08-26 11:50:09.615337:   - EventType: 1
2025-08-26 11:50:09.615337:   - Direction: 0
2025-08-26 11:50:09.615337:   - Antenna: 1
2025-08-26 11:50:09.615337:   - TagFrequency: 0
2025-08-26 11:50:09.615337: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:09.616337:   - UID: E004015305F83C8E
2025-08-26 11:50:09.616337:   - Data: E004015305F83C8E
2025-08-26 11:50:09.616337:   - EventType: 1
2025-08-26 11:50:09.616337:   - Direction: 0
2025-08-26 11:50:09.616337:   - Antenna: 1
2025-08-26 11:50:09.617337:   - TagFrequency: 0
2025-08-26 11:50:10.006331: 多认证管理器: 认证结果显示完成，恢复到监听状态
2025-08-26 11:50:10.006331: 多认证管理器状态变更: listening
2025-08-26 11:50:10.006331: ⚠️ 人脸识别服务未初始化或不可用
2025-08-26 11:50:10.090328: 🔄 开始RFID轮询检查...
2025-08-26 11:50:10.090328: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:10.090328: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:10.091329: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:10.091329: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:10.110328: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:10.110328:   - 设备句柄: 2395416852128
2025-08-26 11:50:10.110328:   - FetchRecords返回值: 0
2025-08-26 11:50:10.111329:   - 报告数量: 2
2025-08-26 11:50:10.111329:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:10.111329:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:10.111329:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:10.111329:   - 设备类型: LSGControlCenter
2025-08-26 11:50:10.111329:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:10.111329:   - 数据长度: 8
2025-08-26 11:50:10.111329:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:10.112328:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:10.112328:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:10.112328:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:10.112328:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:10.112328:   - 设备类型: LSGControlCenter
2025-08-26 11:50:10.112328:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:10.112328:   - 数据长度: 8
2025-08-26 11:50:10.113328:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:10.113328:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:10.113328: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:10.113328:   - 发现标签数量: 2
2025-08-26 11:50:10.113328:   - 标签详情:
2025-08-26 11:50:10.113328:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:10.113328:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:10.113328: RFID扫描: 发现 2 个标签
2025-08-26 11:50:10.114329: 🔍 LSGate ReaderManager收到扫描结果: 2 个UID
2025-08-26 11:50:10.114329: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:10.114329: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:10.115334:   - UID: E004015305F83C8E
2025-08-26 11:50:10.115334:   - Data: E004015305F83C8E
2025-08-26 11:50:10.115334:   - EventType: 1
2025-08-26 11:50:10.116329:   - Direction: 0
2025-08-26 11:50:10.116329:   - Antenna: 1
2025-08-26 11:50:10.116329:   - TagFrequency: 0
2025-08-26 11:50:10.116329: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:10.116329:   - UID: E004015305F83C8E
2025-08-26 11:50:10.116329:   - Data: E004015305F83C8E
2025-08-26 11:50:10.117329:   - EventType: 1
2025-08-26 11:50:10.117329:   - Direction: 0
2025-08-26 11:50:10.117329:   - Antenna: 1
2025-08-26 11:50:10.117329:   - TagFrequency: 0
2025-08-26 11:50:10.590319: 🔄 开始RFID轮询检查...
2025-08-26 11:50:10.590319: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:10.590319: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:10.591320: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:10.591320: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:10.609323: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:10.610320:   - 设备句柄: 2395416852128
2025-08-26 11:50:10.610320:   - FetchRecords返回值: 0
2025-08-26 11:50:10.610320:   - 报告数量: 3
2025-08-26 11:50:10.611321:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:10.611321:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:10.611321:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:10.611321:   - 设备类型: LSGControlCenter
2025-08-26 11:50:10.612320:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:10.612320:   - 数据长度: 8
2025-08-26 11:50:10.612320:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:10.612320:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:10.613320:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:10.613320:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:10.613320:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:10.613320:   - 设备类型: LSGControlCenter
2025-08-26 11:50:10.613320:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:10.614320:   - 数据长度: 8
2025-08-26 11:50:10.614320:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:10.614320:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:10.614320:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:10.614320:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:10.614320:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:10.614320:   - 设备类型: LSGControlCenter
2025-08-26 11:50:10.615320:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:10.615320:   - 数据长度: 8
2025-08-26 11:50:10.615320:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:10.615320:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:10.615320: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:10.615320:   - 发现标签数量: 3
2025-08-26 11:50:10.616319:   - 标签详情:
2025-08-26 11:50:10.616319:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:10.616319:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:10.616319:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:10.616319: RFID扫描: 发现 3 个标签
2025-08-26 11:50:10.616319: 🔍 LSGate ReaderManager收到扫描结果: 3 个UID
2025-08-26 11:50:10.616319: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:10.617320: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:10.617320:   - UID: E004015305F83C8E
2025-08-26 11:50:10.617320:   - Data: E004015305F83C8E
2025-08-26 11:50:10.617320:   - EventType: 1
2025-08-26 11:50:10.617320:   - Direction: 0
2025-08-26 11:50:10.617320:   - Antenna: 1
2025-08-26 11:50:10.618320:   - TagFrequency: 0
2025-08-26 11:50:10.618320: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:10.618320:   - UID: E004015305F83C8E
2025-08-26 11:50:10.618320:   - Data: E004015305F83C8E
2025-08-26 11:50:10.618320:   - EventType: 1
2025-08-26 11:50:10.618320:   - Direction: 0
2025-08-26 11:50:10.618320:   - Antenna: 1
2025-08-26 11:50:10.619320:   - TagFrequency: 0
2025-08-26 11:50:10.619320: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:10.619320:   - UID: E004015305F83C8E
2025-08-26 11:50:10.619320:   - Data: E004015305F83C8E
2025-08-26 11:50:10.619320:   - EventType: 1
2025-08-26 11:50:10.620323:   - Direction: 0
2025-08-26 11:50:10.620323:   - Antenna: 1
2025-08-26 11:50:10.621323:   - TagFrequency: 0
2025-08-26 11:50:11.090311: 🔄 开始RFID轮询检查...
2025-08-26 11:50:11.090311: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:11.090311: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:11.090311: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:11.091311: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:11.111311: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:11.111311:   - 设备句柄: 2395416852128
2025-08-26 11:50:11.111311:   - FetchRecords返回值: 0
2025-08-26 11:50:11.112311:   - 报告数量: 3
2025-08-26 11:50:11.112311:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:11.112311:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:11.112311:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:11.112311:   - 设备类型: LSGControlCenter
2025-08-26 11:50:11.112311:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:11.113311:   - 数据长度: 8
2025-08-26 11:50:11.113311:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:11.113311:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:11.113311:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:11.113311:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:11.113311:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:11.113311:   - 设备类型: LSGControlCenter
2025-08-26 11:50:11.114311:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:11.114311:   - 数据长度: 8
2025-08-26 11:50:11.114311:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:11.114311:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:11.114311:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:11.114311:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:11.114311:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:11.115311:   - 设备类型: LSGControlCenter
2025-08-26 11:50:11.115311:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:11.115311:   - 数据长度: 8
2025-08-26 11:50:11.115311:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:11.115311:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:11.115311: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:11.115311:   - 发现标签数量: 3
2025-08-26 11:50:11.116310:   - 标签详情:
2025-08-26 11:50:11.116310:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:11.116310:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:11.116310:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:11.116310: RFID扫描: 发现 3 个标签
2025-08-26 11:50:11.116310: 🔍 LSGate ReaderManager收到扫描结果: 3 个UID
2025-08-26 11:50:11.116310: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:11.116310: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:11.117310:   - UID: E004015305F83C8E
2025-08-26 11:50:11.117310:   - Data: E004015305F83C8E
2025-08-26 11:50:11.117310:   - EventType: 1
2025-08-26 11:50:11.117310:   - Direction: 0
2025-08-26 11:50:11.117310:   - Antenna: 1
2025-08-26 11:50:11.117310:   - TagFrequency: 0
2025-08-26 11:50:11.117310: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:11.118311:   - UID: E004015305F83C8E
2025-08-26 11:50:11.118311:   - Data: E004015305F83C8E
2025-08-26 11:50:11.118311:   - EventType: 1
2025-08-26 11:50:11.118311:   - Direction: 0
2025-08-26 11:50:11.118311:   - Antenna: 1
2025-08-26 11:50:11.119313:   - TagFrequency: 0
2025-08-26 11:50:11.119313: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:11.120311:   - UID: E004015305F83C8E
2025-08-26 11:50:11.120311:   - Data: E004015305F83C8E
2025-08-26 11:50:11.120311:   - EventType: 1
2025-08-26 11:50:11.120311:   - Direction: 0
2025-08-26 11:50:11.120311:   - Antenna: 1
2025-08-26 11:50:11.120311:   - TagFrequency: 0
2025-08-26 11:50:11.591302: 🔄 开始RFID轮询检查...
2025-08-26 11:50:11.591302: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:11.591302: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:11.591302: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:11.591302: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:11.610302: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:11.611304:   - 设备句柄: 2395416852128
2025-08-26 11:50:11.611304:   - FetchRecords返回值: 0
2025-08-26 11:50:11.611304:   - 报告数量: 3
2025-08-26 11:50:11.611304:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:11.611304:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:11.611304:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:11.612304:   - 设备类型: LSGControlCenter
2025-08-26 11:50:11.612304:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:11.612304:   - 数据长度: 8
2025-08-26 11:50:11.612304:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:11.613305:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:11.613305:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:11.614303:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:11.614303:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:11.614303:   - 设备类型: LSGControlCenter
2025-08-26 11:50:11.614303:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:11.614303:   - 数据长度: 8
2025-08-26 11:50:11.614303:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:11.615304:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:11.615304:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:11.615304:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:11.615304:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:11.615304:   - 设备类型: LSGControlCenter
2025-08-26 11:50:11.616303:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:11.616303:   - 数据长度: 8
2025-08-26 11:50:11.616303:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:11.616303:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:11.617302: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:11.617302:   - 发现标签数量: 3
2025-08-26 11:50:11.617302:   - 标签详情:
2025-08-26 11:50:11.617302:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:11.617302:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:11.618303:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:11.618303: RFID扫描: 发现 3 个标签
2025-08-26 11:50:11.618303: 🔍 LSGate ReaderManager收到扫描结果: 3 个UID
2025-08-26 11:50:11.618303: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:11.618303: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:11.618303:   - UID: E004015305F83C8E
2025-08-26 11:50:11.619303:   - Data: E004015305F83C8E
2025-08-26 11:50:11.619303:   - EventType: 1
2025-08-26 11:50:11.619303:   - Direction: 0
2025-08-26 11:50:11.619303:   - Antenna: 1
2025-08-26 11:50:11.619303:   - TagFrequency: 0
2025-08-26 11:50:11.619303: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:11.619303:   - UID: E004015305F83C8E
2025-08-26 11:50:11.620302:   - Data: E004015305F83C8E
2025-08-26 11:50:11.620302:   - EventType: 1
2025-08-26 11:50:11.620302:   - Direction: 0
2025-08-26 11:50:11.620302:   - Antenna: 1
2025-08-26 11:50:11.620302:   - TagFrequency: 0
2025-08-26 11:50:11.620302: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:11.621304:   - UID: E004015305F83C8E
2025-08-26 11:50:11.621304:   - Data: E004015305F83C8E
2025-08-26 11:50:11.621304:   - EventType: 1
2025-08-26 11:50:11.621304:   - Direction: 0
2025-08-26 11:50:11.621304:   - Antenna: 1
2025-08-26 11:50:11.622303:   - TagFrequency: 0
2025-08-26 11:50:11.961299: ⏰ 10秒数据收集完成，开始书籍检查... 时间: 2025-08-26 11:50:11.961299
2025-08-26 11:50:11.962301: 🛑 停止出馆数据收集，收集到UID数量: 0
2025-08-26 11:50:11.962301: 🔧 停止时间: 2025-08-26 11:50:11.961299
2025-08-26 11:50:11.963297: 📡 停止从共享池收集数据...
2025-08-26 11:50:11.963297: 🔧 保持RFID扫描运行，只停止从共享池收集数据
2025-08-26 11:50:11.963297: 📡 开始从RFID服务收集UID数据...
2025-08-26 11:50:11.963297: 📊 从HWTagProvider获取UID，标签数量: 1
2025-08-26 11:50:11.964297: 📡 收集到UID: E004015305F83C8E (条码: null)
2025-08-26 11:50:11.964297: 📡 从RFID服务收集到UID数量: 1
2025-08-26 11:50:11.964297: 📋 UID列表: E004015305F83C8E
2025-08-26 11:50:11.964297: 📊 数据收集完成，UID列表: [E004015305F83C8E]
2025-08-26 11:50:11.964297: 🔍 开始三步书籍检查，UID数量: 1
2025-08-26 11:50:11.965298: 闸机状态变更: GateState.exitScanning -> GateState.exitChecking
2025-08-26 11:50:11.965298: 闸机状态更新: GateState.exitScanning -> GateState.exitChecking
2025-08-26 11:50:11.965298: 🚀 开始完整的三步书籍检查流程，UID数量: 1
2025-08-26 11:50:11.966304: 🔍 第一步：检查白名单，UID数量: 1
2025-08-26 11:50:11.966304: [channel_1] 收到闸机事件: state_changed
2025-08-26 11:50:11.966304: 📨 收到GateCoordinator事件: state_changed
2025-08-26 11:50:11.967296: 闸机状态变更: GateState.exitChecking
2025-08-26 11:50:11.967296: 🎨 处理状态变更UI: exitChecking
2025-08-26 11:50:11.967296: 未处理的状态变更UI: exitChecking
2025-08-26 11:50:11.968296: 📤 白名单检查请求: http://***************:9000/tunano/ldc/white/tag/whitelist/contain
2025-08-26 11:50:11.968296: 📤 请求数据: {"Taglist":[{"Tid":"E004015305F83C8E"}]}
2025-08-26 11:50:12.090302: 🔄 开始RFID轮询检查...
2025-08-26 11:50:12.090302: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:12.090302: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:12.091294: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:12.091294: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:12.110293: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:12.110293:   - 设备句柄: 2395416852128
2025-08-26 11:50:12.111294:   - FetchRecords返回值: 0
2025-08-26 11:50:12.111294:   - 报告数量: 4
2025-08-26 11:50:12.111294:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:12.111294:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:12.111294:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:12.112294:   - 设备类型: LSGControlCenter
2025-08-26 11:50:12.112294:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:12.112294:   - 数据长度: 8
2025-08-26 11:50:12.112294:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:12.112294:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:12.112294:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:12.112294:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:12.113293:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:12.113293:   - 设备类型: LSGControlCenter
2025-08-26 11:50:12.113293:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:12.113293:   - 数据长度: 8
2025-08-26 11:50:12.113293:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:12.113293:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:12.113293:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:12.114293:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:12.114293:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:12.114293:   - 设备类型: LSGControlCenter
2025-08-26 11:50:12.114293:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:12.114293:   - 数据长度: 8
2025-08-26 11:50:12.114293:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:12.114293:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:12.115295:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:12.115295:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:12.115295:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:12.115295:   - 设备类型: LSGControlCenter
2025-08-26 11:50:12.115295:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:12.115295:   - 数据长度: 8
2025-08-26 11:50:12.116293:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:12.116293:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:12.116293: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:12.116293:   - 发现标签数量: 4
2025-08-26 11:50:12.116293:   - 标签详情:
2025-08-26 11:50:12.116293:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:12.117295:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:12.117295:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:12.117295:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:12.117295: RFID扫描: 发现 4 个标签
2025-08-26 11:50:12.117295: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:12.118299: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:12.118299: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:12.119297:   - UID: E004015305F83C8E
2025-08-26 11:50:12.119297:   - Data: E004015305F83C8E
2025-08-26 11:50:12.119297:   - EventType: 1
2025-08-26 11:50:12.120301:   - Direction: 0
2025-08-26 11:50:12.120301:   - Antenna: 1
2025-08-26 11:50:12.120301:   - TagFrequency: 0
2025-08-26 11:50:12.120301: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:12.121294:   - UID: E004015305F83C8E
2025-08-26 11:50:12.121294:   - Data: E004015305F83C8E
2025-08-26 11:50:12.121294:   - EventType: 1
2025-08-26 11:50:12.121294:   - Direction: 0
2025-08-26 11:50:12.121294:   - Antenna: 1
2025-08-26 11:50:12.122294:   - TagFrequency: 0
2025-08-26 11:50:12.122294: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:12.122294:   - UID: E004015305F83C8E
2025-08-26 11:50:12.122294:   - Data: E004015305F83C8E
2025-08-26 11:50:12.123293:   - EventType: 1
2025-08-26 11:50:12.123293:   - Direction: 0
2025-08-26 11:50:12.123293:   - Antenna: 1
2025-08-26 11:50:12.123293:   - TagFrequency: 0
2025-08-26 11:50:12.123293: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:12.124294:   - UID: E004015305F83C8E
2025-08-26 11:50:12.124294:   - Data: E004015305F83C8E
2025-08-26 11:50:12.124294:   - EventType: 1
2025-08-26 11:50:12.124294:   - Direction: 0
2025-08-26 11:50:12.124294:   - Antenna: 1
2025-08-26 11:50:12.124294:   - TagFrequency: 0
2025-08-26 11:50:12.591285: 🔄 开始RFID轮询检查...
2025-08-26 11:50:12.591285: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:12.592291: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:12.592291: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:12.592291: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:12.610285: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:12.610285:   - 设备句柄: 2395416852128
2025-08-26 11:50:12.610285:   - FetchRecords返回值: 0
2025-08-26 11:50:12.611285:   - 报告数量: 4
2025-08-26 11:50:12.611285:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:12.611285:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:12.611285:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:12.611285:   - 设备类型: LSGControlCenter
2025-08-26 11:50:12.611285:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:12.612285:   - 数据长度: 8
2025-08-26 11:50:12.612285:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:12.612285:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:12.612285:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:12.612285:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:12.612285:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:12.612285:   - 设备类型: LSGControlCenter
2025-08-26 11:50:12.613284:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:12.613284:   - 数据长度: 8
2025-08-26 11:50:12.613284:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:12.613284:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:12.613284:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:12.613284:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:12.613284:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:12.614284:   - 设备类型: LSGControlCenter
2025-08-26 11:50:12.614284:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:12.614284:   - 数据长度: 8
2025-08-26 11:50:12.614284:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:12.614284:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:12.614284:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:12.614284:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:12.614284:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:12.615284:   - 设备类型: LSGControlCenter
2025-08-26 11:50:12.615284:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:12.615284:   - 数据长度: 8
2025-08-26 11:50:12.615284:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:12.615284:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:12.615284: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:12.615284:   - 发现标签数量: 4
2025-08-26 11:50:12.616285:   - 标签详情:
2025-08-26 11:50:12.616285:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:12.616285:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:12.616285:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:12.616285:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:12.617288: RFID扫描: 发现 4 个标签
2025-08-26 11:50:12.617288: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:12.617288: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:12.618285: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:12.618285:   - UID: E004015305F83C8E
2025-08-26 11:50:12.618285:   - Data: E004015305F83C8E
2025-08-26 11:50:12.618285:   - EventType: 1
2025-08-26 11:50:12.618285:   - Direction: 0
2025-08-26 11:50:12.619285:   - Antenna: 1
2025-08-26 11:50:12.619285:   - TagFrequency: 0
2025-08-26 11:50:12.619285: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:12.619285:   - UID: E004015305F83C8E
2025-08-26 11:50:12.619285:   - Data: E004015305F83C8E
2025-08-26 11:50:12.619285:   - EventType: 1
2025-08-26 11:50:12.619285:   - Direction: 0
2025-08-26 11:50:12.619285:   - Antenna: 1
2025-08-26 11:50:12.620284:   - TagFrequency: 0
2025-08-26 11:50:12.620284: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:12.620284:   - UID: E004015305F83C8E
2025-08-26 11:50:12.620284:   - Data: E004015305F83C8E
2025-08-26 11:50:12.620284:   - EventType: 1
2025-08-26 11:50:12.620284:   - Direction: 0
2025-08-26 11:50:12.620284:   - Antenna: 1
2025-08-26 11:50:12.621284:   - TagFrequency: 0
2025-08-26 11:50:12.621284: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:12.621284:   - UID: E004015305F83C8E
2025-08-26 11:50:12.621284:   - Data: E004015305F83C8E
2025-08-26 11:50:12.621284:   - EventType: 1
2025-08-26 11:50:12.621284:   - Direction: 0
2025-08-26 11:50:12.622284:   - Antenna: 1
2025-08-26 11:50:12.622284:   - TagFrequency: 0
2025-08-26 11:50:12.984280: 📥 白名单检查响应: 502 - <html>
2025-08-26 11:50:12.984280: <head><title>502 Bad Gateway</title></head>
2025-08-26 11:50:12.984280: <body>
2025-08-26 11:50:12.985279: <center><h1>502 Bad Gateway</h1></center>
2025-08-26 11:50:12.985279: <hr><center>nginx/1.24.0</center>
2025-08-26 11:50:12.985279: </body>
2025-08-26 11:50:12.985279: </html>
2025-08-26 11:50:12.985279: 
2025-08-26 11:50:12.985279: ❌ 白名单检查失败: Exception: 白名单检查请求失败: 502
2025-08-26 11:50:12.985279: ❌ 三步检查流程失败: Exception: 白名单检查请求失败: 502
2025-08-26 11:50:12.985279: ✅ 三步检查完成: 书籍检查服务异常，默认允许通过
2025-08-26 11:50:12.986280: 📊 检查结果: 允许通过=true, 不在白名单书籍数量=0
2025-08-26 11:50:12.986280: ✅ 允许出馆: 书籍检查服务异常，默认允许通过
2025-08-26 11:50:12.986280: ✅ 命令顺序正确：出馆流程中且最后命令是到位信号
2025-08-26 11:50:12.986280: 📤 正在发送成功信号到闸机：AA 00 01 01 00 00 48 36
2025-08-26 11:50:12.986280: 📤 准备发送闸机命令: success_signal
2025-08-26 11:50:12.987283: 📤 成功信号已发送（异步）
2025-08-26 11:50:12.987283: 闸机状态变更: GateState.exitChecking -> GateState.exitOver
2025-08-26 11:50:12.988283: 闸机状态更新: GateState.exitChecking -> GateState.exitOver
2025-08-26 11:50:12.988283: 发送原始命令数据: aa 00 01 01 00 00 48 36
2025-08-26 11:50:12.989279: 发送原始数据: aa 00 01 01 00 00 48 36
2025-08-26 11:50:12.989279: 发送数据成功: aa 00 01 01 00 00 48 36
2025-08-26 11:50:12.989279: [channel_1] 收到闸机事件: state_changed
2025-08-26 11:50:12.990279: 📨 收到GateCoordinator事件: state_changed
2025-08-26 11:50:12.990279: 闸机状态变更: GateState.exitOver
2025-08-26 11:50:12.990279: 🎨 处理状态变更UI: exitOver
2025-08-26 11:50:12.990279: 未处理的状态变更UI: exitOver
2025-08-26 11:50:12.990279: 发送数据成功: aa 00 01 01 00 00 48 36
2025-08-26 11:50:12.991280: 闸机命令发送成功: success_signal
2025-08-26 11:50:12.991280: ✅ 闸机命令 success_signal 发送成功
2025-08-26 11:50:12.991280: [channel_1] 收到闸机事件: exit_allowed
2025-08-26 11:50:12.991280: 📨 收到GateCoordinator事件: exit_allowed
2025-08-26 11:50:12.992281: 页面状态变更: SilencePageState.exitAllowed
2025-08-26 11:50:13.013278: 接收到数据: aa 00 01 81 00 00 49 de
2025-08-26 11:50:13.013278: 🔍 接收到串口数据: aa 00 01 81 00 00 49 de
2025-08-26 11:50:13.014278: 🔍 数据长度: 8 字节
2025-08-26 11:50:13.014278: 🔍 预定义命令列表:
2025-08-26 11:50:13.014278:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-26 11:50:13.014278:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-26 11:50:13.014278:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-26 11:50:13.014278:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-26 11:50:13.014278:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-26 11:50:13.014278:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-26 11:50:13.014278:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-26 11:50:13.015277:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-26 11:50:13.015277:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-26 11:50:13.015277:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-26 11:50:13.015277: ❌ 未识别的闸机命令 - 数据不匹配任何预定义命令
2025-08-26 11:50:13.015277: ❌ 接收数据: aa 00 01 81 00 00 49 de
2025-08-26 11:50:13.091276: 🔄 开始RFID轮询检查...
2025-08-26 11:50:13.091276: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:13.091276: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:13.091276: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:13.091276: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:13.111277: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:13.112276:   - 设备句柄: 2395416852128
2025-08-26 11:50:13.112276:   - FetchRecords返回值: 0
2025-08-26 11:50:13.112276:   - 报告数量: 4
2025-08-26 11:50:13.112276:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:13.113276:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:13.113276:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:13.113276:   - 设备类型: LSGControlCenter
2025-08-26 11:50:13.113276:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:13.114277:   - 数据长度: 8
2025-08-26 11:50:13.114277:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:13.114277:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:13.114277:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:13.115277:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:13.115277:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:13.115277:   - 设备类型: LSGControlCenter
2025-08-26 11:50:13.115277:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:13.115277:   - 数据长度: 8
2025-08-26 11:50:13.116276:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:13.116276:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:13.116276:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:13.116276:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:13.116276:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:13.116276:   - 设备类型: LSGControlCenter
2025-08-26 11:50:13.116276:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:13.117280:   - 数据长度: 8
2025-08-26 11:50:13.117280:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:13.117280:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:13.117280:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:13.117280:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:13.117280:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:13.117280:   - 设备类型: LSGControlCenter
2025-08-26 11:50:13.118276:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:13.118276:   - 数据长度: 8
2025-08-26 11:50:13.118276:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:13.118276:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:13.118276: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:13.119276:   - 发现标签数量: 4
2025-08-26 11:50:13.119276:   - 标签详情:
2025-08-26 11:50:13.119276:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:13.119276:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:13.119276:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:13.119276:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:13.119276: RFID扫描: 发现 4 个标签
2025-08-26 11:50:13.120276: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:13.120276: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:13.120276: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:13.120276:   - UID: E004015305F83C8E
2025-08-26 11:50:13.120276:   - Data: E004015305F83C8E
2025-08-26 11:50:13.120276:   - EventType: 1
2025-08-26 11:50:13.121276:   - Direction: 0
2025-08-26 11:50:13.121276:   - Antenna: 1
2025-08-26 11:50:13.121276:   - TagFrequency: 0
2025-08-26 11:50:13.122291: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:13.122291:   - UID: E004015305F83C8E
2025-08-26 11:50:13.122291:   - Data: E004015305F83C8E
2025-08-26 11:50:13.123282:   - EventType: 1
2025-08-26 11:50:13.123282:   - Direction: 0
2025-08-26 11:50:13.123282:   - Antenna: 1
2025-08-26 11:50:13.124277:   - TagFrequency: 0
2025-08-26 11:50:13.124277: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:13.124277:   - UID: E004015305F83C8E
2025-08-26 11:50:13.124277:   - Data: E004015305F83C8E
2025-08-26 11:50:13.125277:   - EventType: 1
2025-08-26 11:50:13.125277:   - Direction: 0
2025-08-26 11:50:13.125277:   - Antenna: 1
2025-08-26 11:50:13.125277:   - TagFrequency: 0
2025-08-26 11:50:13.125277: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:13.126277:   - UID: E004015305F83C8E
2025-08-26 11:50:13.126277:   - Data: E004015305F83C8E
2025-08-26 11:50:13.126277:   - EventType: 1
2025-08-26 11:50:13.126277:   - Direction: 0
2025-08-26 11:50:13.126277:   - Antenna: 1
2025-08-26 11:50:13.127276:   - TagFrequency: 0
2025-08-26 11:50:13.591268: 🔄 开始RFID轮询检查...
2025-08-26 11:50:13.591268: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:13.592269: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:13.592269: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:13.592269: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:13.610267: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:13.610267:   - 设备句柄: 2395416852128
2025-08-26 11:50:13.611268:   - FetchRecords返回值: 0
2025-08-26 11:50:13.611268:   - 报告数量: 4
2025-08-26 11:50:13.611268:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:13.611268:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:13.611268:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:13.611268:   - 设备类型: LSGControlCenter
2025-08-26 11:50:13.611268:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:13.612267:   - 数据长度: 8
2025-08-26 11:50:13.612267:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:13.612267:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:13.612267:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:13.612267:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:13.612267:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:13.612267:   - 设备类型: LSGControlCenter
2025-08-26 11:50:13.613267:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:13.613267:   - 数据长度: 8
2025-08-26 11:50:13.613267:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:13.613267:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:13.613267:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:13.613267:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:13.613267:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:13.614267:   - 设备类型: LSGControlCenter
2025-08-26 11:50:13.614267:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:13.614267:   - 数据长度: 8
2025-08-26 11:50:13.614267:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:13.614267:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:13.614267:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:13.615267:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:13.615267:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:13.615267:   - 设备类型: LSGControlCenter
2025-08-26 11:50:13.616270:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:13.616270:   - 数据长度: 8
2025-08-26 11:50:13.617270:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:13.617270:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:13.617270: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:13.618268:   - 发现标签数量: 4
2025-08-26 11:50:13.618268:   - 标签详情:
2025-08-26 11:50:13.618268:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:13.618268:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:13.619268:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:13.619268:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:13.619268: RFID扫描: 发现 4 个标签
2025-08-26 11:50:13.619268: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:13.619268: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:13.620268: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:13.620268:   - UID: E004015305F83C8E
2025-08-26 11:50:13.620268:   - Data: E004015305F83C8E
2025-08-26 11:50:13.620268:   - EventType: 1
2025-08-26 11:50:13.620268:   - Direction: 0
2025-08-26 11:50:13.621268:   - Antenna: 1
2025-08-26 11:50:13.621268:   - TagFrequency: 0
2025-08-26 11:50:13.621268: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:13.621268:   - UID: E004015305F83C8E
2025-08-26 11:50:13.621268:   - Data: E004015305F83C8E
2025-08-26 11:50:13.621268:   - EventType: 1
2025-08-26 11:50:13.622267:   - Direction: 0
2025-08-26 11:50:13.622267:   - Antenna: 1
2025-08-26 11:50:13.622267:   - TagFrequency: 0
2025-08-26 11:50:13.622267: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:13.622267:   - UID: E004015305F83C8E
2025-08-26 11:50:13.623267:   - Data: E004015305F83C8E
2025-08-26 11:50:13.623267:   - EventType: 1
2025-08-26 11:50:13.623267:   - Direction: 0
2025-08-26 11:50:13.623267:   - Antenna: 1
2025-08-26 11:50:13.623267:   - TagFrequency: 0
2025-08-26 11:50:13.623267: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:13.623267:   - UID: E004015305F83C8E
2025-08-26 11:50:13.624267:   - Data: E004015305F83C8E
2025-08-26 11:50:13.624267:   - EventType: 1
2025-08-26 11:50:13.624267:   - Direction: 0
2025-08-26 11:50:13.624267:   - Antenna: 1
2025-08-26 11:50:13.624267:   - TagFrequency: 0
2025-08-26 11:50:14.091259: 🔄 开始RFID轮询检查...
2025-08-26 11:50:14.091259: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:14.091259: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:14.091259: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:14.092259: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:14.110259: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:14.111259:   - 设备句柄: 2395416852128
2025-08-26 11:50:14.111259:   - FetchRecords返回值: 0
2025-08-26 11:50:14.111259:   - 报告数量: 4
2025-08-26 11:50:14.111259:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:14.112259:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:14.112259:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:14.112259:   - 设备类型: LSGControlCenter
2025-08-26 11:50:14.112259:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:14.112259:   - 数据长度: 8
2025-08-26 11:50:14.113259:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:14.113259:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:14.113259:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:14.113259:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:14.113259:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:14.113259:   - 设备类型: LSGControlCenter
2025-08-26 11:50:14.114259:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:14.114259:   - 数据长度: 8
2025-08-26 11:50:14.114259:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:14.114259:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:14.114259:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:14.114259:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:14.115259:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:14.115259:   - 设备类型: LSGControlCenter
2025-08-26 11:50:14.115259:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:14.115259:   - 数据长度: 8
2025-08-26 11:50:14.115259:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:14.115259:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:14.115259:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:14.116258:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:14.116258:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:14.116258:   - 设备类型: LSGControlCenter
2025-08-26 11:50:14.116258:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:14.116258:   - 数据长度: 8
2025-08-26 11:50:14.116258:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:14.116258:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:14.116258: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:14.117258:   - 发现标签数量: 4
2025-08-26 11:50:14.117258:   - 标签详情:
2025-08-26 11:50:14.117258:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:14.117258:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:14.117258:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:14.117258:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:14.117258: RFID扫描: 发现 4 个标签
2025-08-26 11:50:14.117258: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:14.118258: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:14.118258: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:14.118258:   - UID: E004015305F83C8E
2025-08-26 11:50:14.118258:   - Data: E004015305F83C8E
2025-08-26 11:50:14.118258:   - EventType: 1
2025-08-26 11:50:14.118258:   - Direction: 0
2025-08-26 11:50:14.118258:   - Antenna: 1
2025-08-26 11:50:14.118258:   - TagFrequency: 0
2025-08-26 11:50:14.119258: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:14.119258:   - UID: E004015305F83C8E
2025-08-26 11:50:14.119258:   - Data: E004015305F83C8E
2025-08-26 11:50:14.119258:   - EventType: 1
2025-08-26 11:50:14.119258:   - Direction: 0
2025-08-26 11:50:14.119258:   - Antenna: 1
2025-08-26 11:50:14.119258:   - TagFrequency: 0
2025-08-26 11:50:14.120258: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:14.120258:   - UID: E004015305F83C8E
2025-08-26 11:50:14.120258:   - Data: E004015305F83C8E
2025-08-26 11:50:14.120258:   - EventType: 1
2025-08-26 11:50:14.120258:   - Direction: 0
2025-08-26 11:50:14.120258:   - Antenna: 1
2025-08-26 11:50:14.120258:   - TagFrequency: 0
2025-08-26 11:50:14.120258: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:14.121259:   - UID: E004015305F83C8E
2025-08-26 11:50:14.121259:   - Data: E004015305F83C8E
2025-08-26 11:50:14.121259:   - EventType: 1
2025-08-26 11:50:14.121259:   - Direction: 0
2025-08-26 11:50:14.121259:   - Antenna: 1
2025-08-26 11:50:14.121259:   - TagFrequency: 0
2025-08-26 11:50:14.591250: 🔄 开始RFID轮询检查...
2025-08-26 11:50:14.591250: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:14.591250: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:14.591250: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:14.591250: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:14.610250: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:14.610250:   - 设备句柄: 2395416852128
2025-08-26 11:50:14.611251:   - FetchRecords返回值: 0
2025-08-26 11:50:14.611251:   - 报告数量: 4
2025-08-26 11:50:14.611251:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:14.611251:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:14.612251:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:14.612251:   - 设备类型: LSGControlCenter
2025-08-26 11:50:14.612251:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:14.612251:   - 数据长度: 8
2025-08-26 11:50:14.612251:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:14.613250:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:14.613250:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:14.613250:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:14.613250:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:14.613250:   - 设备类型: LSGControlCenter
2025-08-26 11:50:14.613250:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:14.613250:   - 数据长度: 8
2025-08-26 11:50:14.614250:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:14.614250:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:14.614250:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:14.614250:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:14.614250:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:14.614250:   - 设备类型: LSGControlCenter
2025-08-26 11:50:14.614250:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:14.614250:   - 数据长度: 8
2025-08-26 11:50:14.615250:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:14.615250:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:14.615250:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:14.615250:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:14.615250:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:14.615250:   - 设备类型: LSGControlCenter
2025-08-26 11:50:14.616250:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:14.616250:   - 数据长度: 8
2025-08-26 11:50:14.616250:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:14.616250:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:14.616250: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:14.616250:   - 发现标签数量: 4
2025-08-26 11:50:14.616250:   - 标签详情:
2025-08-26 11:50:14.617250:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:14.617250:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:14.617250:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:14.617250:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:14.617250: RFID扫描: 发现 4 个标签
2025-08-26 11:50:14.617250: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:14.618251: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:14.618251: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:14.619251:   - UID: E004015305F83C8E
2025-08-26 11:50:14.619251:   - Data: E004015305F83C8E
2025-08-26 11:50:14.620251:   - EventType: 1
2025-08-26 11:50:14.620251:   - Direction: 0
2025-08-26 11:50:14.621251:   - Antenna: 1
2025-08-26 11:50:14.621251:   - TagFrequency: 0
2025-08-26 11:50:14.621251: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:14.621251:   - UID: E004015305F83C8E
2025-08-26 11:50:14.621251:   - Data: E004015305F83C8E
2025-08-26 11:50:14.622251:   - EventType: 1
2025-08-26 11:50:14.622251:   - Direction: 0
2025-08-26 11:50:14.622251:   - Antenna: 1
2025-08-26 11:50:14.622251:   - TagFrequency: 0
2025-08-26 11:50:14.623250: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:14.623250:   - UID: E004015305F83C8E
2025-08-26 11:50:14.623250:   - Data: E004015305F83C8E
2025-08-26 11:50:14.623250:   - EventType: 1
2025-08-26 11:50:14.623250:   - Direction: 0
2025-08-26 11:50:14.624250:   - Antenna: 1
2025-08-26 11:50:14.624250:   - TagFrequency: 0
2025-08-26 11:50:14.624250: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:14.624250:   - UID: E004015305F83C8E
2025-08-26 11:50:14.624250:   - Data: E004015305F83C8E
2025-08-26 11:50:14.624250:   - EventType: 1
2025-08-26 11:50:14.625250:   - Direction: 0
2025-08-26 11:50:14.625250:   - Antenna: 1
2025-08-26 11:50:14.625250:   - TagFrequency: 0
2025-08-26 11:50:15.091241: 🔄 开始RFID轮询检查...
2025-08-26 11:50:15.091241: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:15.092243: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:15.092243: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:15.092243: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:15.111241: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:15.111241:   - 设备句柄: 2395416852128
2025-08-26 11:50:15.112243:   - FetchRecords返回值: 0
2025-08-26 11:50:15.112243:   - 报告数量: 4
2025-08-26 11:50:15.112243:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:15.113246:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:15.113246:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:15.113246:   - 设备类型: LSGControlCenter
2025-08-26 11:50:15.114243:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:15.114243:   - 数据长度: 8
2025-08-26 11:50:15.114243:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:15.114243:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:15.115242:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:15.115242:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:15.115242:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:15.115242:   - 设备类型: LSGControlCenter
2025-08-26 11:50:15.116242:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:15.116242:   - 数据长度: 8
2025-08-26 11:50:15.116242:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:15.116242:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:15.116242:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:15.116242:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:15.117241:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:15.117241:   - 设备类型: LSGControlCenter
2025-08-26 11:50:15.117241:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:15.117241:   - 数据长度: 8
2025-08-26 11:50:15.118247:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:15.118247:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:15.118247:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:15.118247:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:15.118247:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:15.118247:   - 设备类型: LSGControlCenter
2025-08-26 11:50:15.119241:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:15.119241:   - 数据长度: 8
2025-08-26 11:50:15.119241:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:15.119241:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:15.119241: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:15.119241:   - 发现标签数量: 4
2025-08-26 11:50:15.119241:   - 标签详情:
2025-08-26 11:50:15.120241:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:15.120241:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:15.120241:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:15.120241:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:15.120241: RFID扫描: 发现 4 个标签
2025-08-26 11:50:15.120241: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:15.120241: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:15.121241: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:15.121241:   - UID: E004015305F83C8E
2025-08-26 11:50:15.121241:   - Data: E004015305F83C8E
2025-08-26 11:50:15.121241:   - EventType: 1
2025-08-26 11:50:15.121241:   - Direction: 0
2025-08-26 11:50:15.121241:   - Antenna: 1
2025-08-26 11:50:15.121241:   - TagFrequency: 0
2025-08-26 11:50:15.121241: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:15.122241:   - UID: E004015305F83C8E
2025-08-26 11:50:15.122241:   - Data: E004015305F83C8E
2025-08-26 11:50:15.122241:   - EventType: 1
2025-08-26 11:50:15.122241:   - Direction: 0
2025-08-26 11:50:15.122241:   - Antenna: 1
2025-08-26 11:50:15.122241:   - TagFrequency: 0
2025-08-26 11:50:15.122241: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:15.122241:   - UID: E004015305F83C8E
2025-08-26 11:50:15.123241:   - Data: E004015305F83C8E
2025-08-26 11:50:15.123241:   - EventType: 1
2025-08-26 11:50:15.123241:   - Direction: 0
2025-08-26 11:50:15.123241:   - Antenna: 1
2025-08-26 11:50:15.123241:   - TagFrequency: 0
2025-08-26 11:50:15.123241: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:15.123241:   - UID: E004015305F83C8E
2025-08-26 11:50:15.124241:   - Data: E004015305F83C8E
2025-08-26 11:50:15.124241:   - EventType: 1
2025-08-26 11:50:15.124241:   - Direction: 0
2025-08-26 11:50:15.124241:   - Antenna: 1
2025-08-26 11:50:15.124241:   - TagFrequency: 0
2025-08-26 11:50:15.590232: 🔄 开始RFID轮询检查...
2025-08-26 11:50:15.590232: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:15.590232: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:15.590232: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:15.590232: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:15.611232: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:15.611232:   - 设备句柄: 2395416852128
2025-08-26 11:50:15.611232:   - FetchRecords返回值: 0
2025-08-26 11:50:15.611232:   - 报告数量: 4
2025-08-26 11:50:15.612233:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:15.612233:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:15.612233:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:15.612233:   - 设备类型: LSGControlCenter
2025-08-26 11:50:15.612233:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:15.612233:   - 数据长度: 8
2025-08-26 11:50:15.612233:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:15.613232:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:15.613232:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:15.613232:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:15.613232:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:15.613232:   - 设备类型: LSGControlCenter
2025-08-26 11:50:15.614241:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:15.614241:   - 数据长度: 8
2025-08-26 11:50:15.614241:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:15.615233:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:15.615233:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:15.615233:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:15.615233:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:15.616233:   - 设备类型: LSGControlCenter
2025-08-26 11:50:15.616233:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:15.616233:   - 数据长度: 8
2025-08-26 11:50:15.616233:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:15.616233:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:15.616233:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:15.617239:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:15.617239:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:15.617239:   - 设备类型: LSGControlCenter
2025-08-26 11:50:15.617239:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:15.617239:   - 数据长度: 8
2025-08-26 11:50:15.617239:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:15.618233:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:15.618233: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:15.618233:   - 发现标签数量: 4
2025-08-26 11:50:15.618233:   - 标签详情:
2025-08-26 11:50:15.618233:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:15.619232:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:15.619232:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:15.619232:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:15.619232: RFID扫描: 发现 4 个标签
2025-08-26 11:50:15.619232: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:15.619232: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:15.620234: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:15.620234:   - UID: E004015305F83C8E
2025-08-26 11:50:15.620234:   - Data: E004015305F83C8E
2025-08-26 11:50:15.620234:   - EventType: 1
2025-08-26 11:50:15.620234:   - Direction: 0
2025-08-26 11:50:15.620234:   - Antenna: 1
2025-08-26 11:50:15.620234:   - TagFrequency: 0
2025-08-26 11:50:15.620234: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:15.621233:   - UID: E004015305F83C8E
2025-08-26 11:50:15.621233:   - Data: E004015305F83C8E
2025-08-26 11:50:15.621233:   - EventType: 1
2025-08-26 11:50:15.621233:   - Direction: 0
2025-08-26 11:50:15.621233:   - Antenna: 1
2025-08-26 11:50:15.622233:   - TagFrequency: 0
2025-08-26 11:50:15.622233: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:15.622233:   - UID: E004015305F83C8E
2025-08-26 11:50:15.623237:   - Data: E004015305F83C8E
2025-08-26 11:50:15.623237:   - EventType: 1
2025-08-26 11:50:15.624232:   - Direction: 0
2025-08-26 11:50:15.624232:   - Antenna: 1
2025-08-26 11:50:15.624232:   - TagFrequency: 0
2025-08-26 11:50:15.624232: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:15.625233:   - UID: E004015305F83C8E
2025-08-26 11:50:15.625233:   - Data: E004015305F83C8E
2025-08-26 11:50:15.625233:   - EventType: 1
2025-08-26 11:50:15.625233:   - Direction: 0
2025-08-26 11:50:15.626233:   - Antenna: 1
2025-08-26 11:50:15.626233:   - TagFrequency: 0
2025-08-26 11:50:15.985226: 模拟出馆完成，状态重置为idle
2025-08-26 11:50:15.985226: 闸机状态变更: GateState.exitOver -> GateState.idle
2025-08-26 11:50:15.985226: 闸机状态更新: GateState.exitOver -> GateState.idle
2025-08-26 11:50:15.985226: [channel_1] 收到闸机事件: state_changed
2025-08-26 11:50:15.986227: 📨 收到GateCoordinator事件: state_changed
2025-08-26 11:50:15.986227: 闸机状态变更: GateState.idle
2025-08-26 11:50:15.986227: 🎨 处理状态变更UI: idle
2025-08-26 11:50:15.986227: 页面状态变更: SilencePageState.welcome
2025-08-26 11:50:16.091225: 🔄 开始RFID轮询检查...
2025-08-26 11:50:16.092230: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:16.093224: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:16.093224: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:16.093224: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:16.110224: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:16.110224:   - 设备句柄: 2395416852128
2025-08-26 11:50:16.111224:   - FetchRecords返回值: 0
2025-08-26 11:50:16.111224:   - 报告数量: 4
2025-08-26 11:50:16.111224:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:16.111224:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:16.111224:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:16.111224:   - 设备类型: LSGControlCenter
2025-08-26 11:50:16.111224:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:16.112224:   - 数据长度: 8
2025-08-26 11:50:16.112224:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:16.112224:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:16.112224:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:16.112224:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:16.112224:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:16.112224:   - 设备类型: LSGControlCenter
2025-08-26 11:50:16.112224:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:16.113224:   - 数据长度: 8
2025-08-26 11:50:16.113224:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:16.113224:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:16.113224:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:16.113224:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:16.113224:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:16.113224:   - 设备类型: LSGControlCenter
2025-08-26 11:50:16.114224:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:16.114224:   - 数据长度: 8
2025-08-26 11:50:16.114224:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:16.114224:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:16.114224:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:16.114224:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:16.114224:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:16.114224:   - 设备类型: LSGControlCenter
2025-08-26 11:50:16.115224:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:16.115224:   - 数据长度: 8
2025-08-26 11:50:16.115224:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:16.115224:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:16.115224: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:16.115224:   - 发现标签数量: 4
2025-08-26 11:50:16.115224:   - 标签详情:
2025-08-26 11:50:16.115224:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:16.116223:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:16.116223:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:16.116223:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:16.116223: RFID扫描: 发现 4 个标签
2025-08-26 11:50:16.116223: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:16.116223: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:16.116223: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:16.116223:   - UID: E004015305F83C8E
2025-08-26 11:50:16.117224:   - Data: E004015305F83C8E
2025-08-26 11:50:16.117224:   - EventType: 1
2025-08-26 11:50:16.117224:   - Direction: 0
2025-08-26 11:50:16.117224:   - Antenna: 1
2025-08-26 11:50:16.117224:   - TagFrequency: 0
2025-08-26 11:50:16.117224: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:16.117224:   - UID: E004015305F83C8E
2025-08-26 11:50:16.117224:   - Data: E004015305F83C8E
2025-08-26 11:50:16.118224:   - EventType: 1
2025-08-26 11:50:16.118224:   - Direction: 0
2025-08-26 11:50:16.118224:   - Antenna: 1
2025-08-26 11:50:16.118224:   - TagFrequency: 0
2025-08-26 11:50:16.118224: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:16.118224:   - UID: E004015305F83C8E
2025-08-26 11:50:16.118224:   - Data: E004015305F83C8E
2025-08-26 11:50:16.118224:   - EventType: 1
2025-08-26 11:50:16.118224:   - Direction: 0
2025-08-26 11:50:16.119223:   - Antenna: 1
2025-08-26 11:50:16.119223:   - TagFrequency: 0
2025-08-26 11:50:16.119223: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:16.119223:   - UID: E004015305F83C8E
2025-08-26 11:50:16.119223:   - Data: E004015305F83C8E
2025-08-26 11:50:16.119223:   - EventType: 1
2025-08-26 11:50:16.119223:   - Direction: 0
2025-08-26 11:50:16.119223:   - Antenna: 1
2025-08-26 11:50:16.120223:   - TagFrequency: 0
2025-08-26 11:50:16.591215: 🔄 开始RFID轮询检查...
2025-08-26 11:50:16.592217: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:16.592217: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:16.592217: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:16.592217: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:16.610216: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:16.610216:   - 设备句柄: 2395416852128
2025-08-26 11:50:16.611215:   - FetchRecords返回值: 0
2025-08-26 11:50:16.611215:   - 报告数量: 4
2025-08-26 11:50:16.611215:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:16.611215:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:16.611215:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:16.611215:   - 设备类型: LSGControlCenter
2025-08-26 11:50:16.611215:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:16.612220:   - 数据长度: 8
2025-08-26 11:50:16.612220:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:16.612220:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:16.612220:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:16.612220:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:16.612220:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:16.612220:   - 设备类型: LSGControlCenter
2025-08-26 11:50:16.613215:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:16.613215:   - 数据长度: 8
2025-08-26 11:50:16.613215:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:16.613215:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:16.613215:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:16.613215:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:16.613215:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:16.614215:   - 设备类型: LSGControlCenter
2025-08-26 11:50:16.614215:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:16.614215:   - 数据长度: 8
2025-08-26 11:50:16.614215:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:16.614215:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:16.614215:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:16.614215:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:16.615215:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:16.615215:   - 设备类型: LSGControlCenter
2025-08-26 11:50:16.615215:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:16.615215:   - 数据长度: 8
2025-08-26 11:50:16.615215:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:16.615215:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:16.615215: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:16.615215:   - 发现标签数量: 4
2025-08-26 11:50:16.616216:   - 标签详情:
2025-08-26 11:50:16.616216:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:16.616216:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:16.616216:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:16.616216:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:16.617216: RFID扫描: 发现 4 个标签
2025-08-26 11:50:16.617216: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:16.618215: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:16.618215: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:16.618215:   - UID: E004015305F83C8E
2025-08-26 11:50:16.618215:   - Data: E004015305F83C8E
2025-08-26 11:50:16.618215:   - EventType: 1
2025-08-26 11:50:16.619215:   - Direction: 0
2025-08-26 11:50:16.619215:   - Antenna: 1
2025-08-26 11:50:16.619215:   - TagFrequency: 0
2025-08-26 11:50:16.619215: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:16.619215:   - UID: E004015305F83C8E
2025-08-26 11:50:16.619215:   - Data: E004015305F83C8E
2025-08-26 11:50:16.619215:   - EventType: 1
2025-08-26 11:50:16.620215:   - Direction: 0
2025-08-26 11:50:16.620215:   - Antenna: 1
2025-08-26 11:50:16.620215:   - TagFrequency: 0
2025-08-26 11:50:16.620215: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:16.620215:   - UID: E004015305F83C8E
2025-08-26 11:50:16.620215:   - Data: E004015305F83C8E
2025-08-26 11:50:16.620215:   - EventType: 1
2025-08-26 11:50:16.621215:   - Direction: 0
2025-08-26 11:50:16.621215:   - Antenna: 1
2025-08-26 11:50:16.621215:   - TagFrequency: 0
2025-08-26 11:50:16.621215: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:16.621215:   - UID: E004015305F83C8E
2025-08-26 11:50:16.621215:   - Data: E004015305F83C8E
2025-08-26 11:50:16.621215:   - EventType: 1
2025-08-26 11:50:16.622215:   - Direction: 0
2025-08-26 11:50:16.622215:   - Antenna: 1
2025-08-26 11:50:16.622215:   - TagFrequency: 0
2025-08-26 11:50:17.091206: 🔄 开始RFID轮询检查...
2025-08-26 11:50:17.091206: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:17.091206: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:17.091206: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:17.091206: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:17.111212: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:17.111212:   - 设备句柄: 2395416852128
2025-08-26 11:50:17.112207:   - FetchRecords返回值: 0
2025-08-26 11:50:17.112207:   - 报告数量: 4
2025-08-26 11:50:17.112207:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:17.112207:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:17.112207:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:17.112207:   - 设备类型: LSGControlCenter
2025-08-26 11:50:17.113208:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:17.113208:   - 数据长度: 8
2025-08-26 11:50:17.113208:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:17.113208:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:17.114207:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:17.114207:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:17.114207:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:17.114207:   - 设备类型: LSGControlCenter
2025-08-26 11:50:17.115208:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:17.115208:   - 数据长度: 8
2025-08-26 11:50:17.115208:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:17.115208:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:17.115208:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:17.116207:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:17.116207:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:17.116207:   - 设备类型: LSGControlCenter
2025-08-26 11:50:17.116207:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:17.116207:   - 数据长度: 8
2025-08-26 11:50:17.116207:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:17.117207:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:17.117207:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:17.117207:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:17.117207:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:17.117207:   - 设备类型: LSGControlCenter
2025-08-26 11:50:17.117207:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:17.117207:   - 数据长度: 8
2025-08-26 11:50:17.117207:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:17.118206:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:17.118206: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:17.118206:   - 发现标签数量: 4
2025-08-26 11:50:17.118206:   - 标签详情:
2025-08-26 11:50:17.118206:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:17.118206:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:17.118206:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:17.119208:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:17.119208: RFID扫描: 发现 4 个标签
2025-08-26 11:50:17.119208: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:17.119208: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:17.119208: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:17.120207:   - UID: E004015305F83C8E
2025-08-26 11:50:17.120207:   - Data: E004015305F83C8E
2025-08-26 11:50:17.120207:   - EventType: 1
2025-08-26 11:50:17.120207:   - Direction: 0
2025-08-26 11:50:17.120207:   - Antenna: 1
2025-08-26 11:50:17.120207:   - TagFrequency: 0
2025-08-26 11:50:17.121207: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:17.121207:   - UID: E004015305F83C8E
2025-08-26 11:50:17.121207:   - Data: E004015305F83C8E
2025-08-26 11:50:17.121207:   - EventType: 1
2025-08-26 11:50:17.122213:   - Direction: 0
2025-08-26 11:50:17.122213:   - Antenna: 1
2025-08-26 11:50:17.123209:   - TagFrequency: 0
2025-08-26 11:50:17.123209: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:17.124206:   - UID: E004015305F83C8E
2025-08-26 11:50:17.124206:   - Data: E004015305F83C8E
2025-08-26 11:50:17.124206:   - EventType: 1
2025-08-26 11:50:17.124206:   - Direction: 0
2025-08-26 11:50:17.125208:   - Antenna: 1
2025-08-26 11:50:17.125208:   - TagFrequency: 0
2025-08-26 11:50:17.125208: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:17.125208:   - UID: E004015305F83C8E
2025-08-26 11:50:17.125208:   - Data: E004015305F83C8E
2025-08-26 11:50:17.126207:   - EventType: 1
2025-08-26 11:50:17.126207:   - Direction: 0
2025-08-26 11:50:17.126207:   - Antenna: 1
2025-08-26 11:50:17.126207:   - TagFrequency: 0
2025-08-26 11:50:17.591200: 🔄 开始RFID轮询检查...
2025-08-26 11:50:17.591200: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:17.591200: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:17.592198: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:17.592198: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:17.611197: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:17.611197:   - 设备句柄: 2395416852128
2025-08-26 11:50:17.612204:   - FetchRecords返回值: 0
2025-08-26 11:50:17.612204:   - 报告数量: 4
2025-08-26 11:50:17.612204:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:17.613199:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:17.613199:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:17.613199:   - 设备类型: LSGControlCenter
2025-08-26 11:50:17.613199:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:17.613199:   - 数据长度: 8
2025-08-26 11:50:17.614197:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:17.614197:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:17.614197:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:17.614197:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:17.614197:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:17.614197:   - 设备类型: LSGControlCenter
2025-08-26 11:50:17.614197:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:17.615198:   - 数据长度: 8
2025-08-26 11:50:17.615198:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:17.615198:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:17.615198:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:17.615198:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:17.615198:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:17.615198:   - 设备类型: LSGControlCenter
2025-08-26 11:50:17.616198:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:17.616198:   - 数据长度: 8
2025-08-26 11:50:17.616198:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:17.616198:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:17.616198:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:17.616198:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:17.616198:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:17.616198:   - 设备类型: LSGControlCenter
2025-08-26 11:50:17.617197:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:17.617197:   - 数据长度: 8
2025-08-26 11:50:17.617197:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:17.617197:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:17.617197: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:17.617197:   - 发现标签数量: 4
2025-08-26 11:50:17.617197:   - 标签详情:
2025-08-26 11:50:17.618198:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:17.618198:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:17.618198:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:17.618198:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:17.618198: RFID扫描: 发现 4 个标签
2025-08-26 11:50:17.618198: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:17.618198: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:17.618198: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:17.619198:   - UID: E004015305F83C8E
2025-08-26 11:50:17.619198:   - Data: E004015305F83C8E
2025-08-26 11:50:17.619198:   - EventType: 1
2025-08-26 11:50:17.619198:   - Direction: 0
2025-08-26 11:50:17.619198:   - Antenna: 1
2025-08-26 11:50:17.619198:   - TagFrequency: 0
2025-08-26 11:50:17.619198: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:17.619198:   - UID: E004015305F83C8E
2025-08-26 11:50:17.619198:   - Data: E004015305F83C8E
2025-08-26 11:50:17.620197:   - EventType: 1
2025-08-26 11:50:17.620197:   - Direction: 0
2025-08-26 11:50:17.620197:   - Antenna: 1
2025-08-26 11:50:17.620197:   - TagFrequency: 0
2025-08-26 11:50:17.620197: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:17.621210:   - UID: E004015305F83C8E
2025-08-26 11:50:17.621210:   - Data: E004015305F83C8E
2025-08-26 11:50:17.621210:   - EventType: 1
2025-08-26 11:50:17.622200:   - Direction: 0
2025-08-26 11:50:17.622200:   - Antenna: 1
2025-08-26 11:50:17.622200:   - TagFrequency: 0
2025-08-26 11:50:17.622200: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:17.622200:   - UID: E004015305F83C8E
2025-08-26 11:50:17.623205:   - Data: E004015305F83C8E
2025-08-26 11:50:17.623205:   - EventType: 1
2025-08-26 11:50:17.623205:   - Direction: 0
2025-08-26 11:50:17.623205:   - Antenna: 1
2025-08-26 11:50:17.623205:   - TagFrequency: 0
2025-08-26 11:50:18.091189: 🔄 开始RFID轮询检查...
2025-08-26 11:50:18.091189: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:18.091189: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:18.091189: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:18.091189: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:18.110189: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:18.111190:   - 设备句柄: 2395416852128
2025-08-26 11:50:18.111190:   - FetchRecords返回值: 0
2025-08-26 11:50:18.111190:   - 报告数量: 4
2025-08-26 11:50:18.112190:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:18.112190:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:18.112190:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:18.112190:   - 设备类型: LSGControlCenter
2025-08-26 11:50:18.112190:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:18.113189:   - 数据长度: 8
2025-08-26 11:50:18.113189:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:18.113189:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:18.113189:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:18.113189:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:18.114189:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:18.114189:   - 设备类型: LSGControlCenter
2025-08-26 11:50:18.114189:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:18.114189:   - 数据长度: 8
2025-08-26 11:50:18.115191:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:18.115191:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:18.116191:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:18.116191:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:18.116191:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:18.117189:   - 设备类型: LSGControlCenter
2025-08-26 11:50:18.117189:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:18.117189:   - 数据长度: 8
2025-08-26 11:50:18.117189:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:18.117189:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:18.117189:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:18.118189:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:18.118189:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:18.118189:   - 设备类型: LSGControlCenter
2025-08-26 11:50:18.118189:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:18.118189:   - 数据长度: 8
2025-08-26 11:50:18.118189:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:18.118189:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:18.119189: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:18.119189:   - 发现标签数量: 4
2025-08-26 11:50:18.119189:   - 标签详情:
2025-08-26 11:50:18.119189:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:18.119189:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:18.119189:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:18.119189:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:18.120189: RFID扫描: 发现 4 个标签
2025-08-26 11:50:18.120189: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:18.120189: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:18.120189: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:18.120189:   - UID: E004015305F83C8E
2025-08-26 11:50:18.120189:   - Data: E004015305F83C8E
2025-08-26 11:50:18.120189:   - EventType: 1
2025-08-26 11:50:18.121189:   - Direction: 0
2025-08-26 11:50:18.121189:   - Antenna: 1
2025-08-26 11:50:18.121189:   - TagFrequency: 0
2025-08-26 11:50:18.121189: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:18.121189:   - UID: E004015305F83C8E
2025-08-26 11:50:18.121189:   - Data: E004015305F83C8E
2025-08-26 11:50:18.121189:   - EventType: 1
2025-08-26 11:50:18.121189:   - Direction: 0
2025-08-26 11:50:18.122189:   - Antenna: 1
2025-08-26 11:50:18.122189:   - TagFrequency: 0
2025-08-26 11:50:18.122189: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:18.122189:   - UID: E004015305F83C8E
2025-08-26 11:50:18.122189:   - Data: E004015305F83C8E
2025-08-26 11:50:18.122189:   - EventType: 1
2025-08-26 11:50:18.122189:   - Direction: 0
2025-08-26 11:50:18.123189:   - Antenna: 1
2025-08-26 11:50:18.123189:   - TagFrequency: 0
2025-08-26 11:50:18.123189: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:18.123189:   - UID: E004015305F83C8E
2025-08-26 11:50:18.123189:   - Data: E004015305F83C8E
2025-08-26 11:50:18.123189:   - EventType: 1
2025-08-26 11:50:18.123189:   - Direction: 0
2025-08-26 11:50:18.123189:   - Antenna: 1
2025-08-26 11:50:18.123189:   - TagFrequency: 0
2025-08-26 11:50:18.233187: 接收到数据: aa 00 c9 80 00 00 26 7e
2025-08-26 11:50:18.234188: 🔍 接收到串口数据: aa 00 c9 80 00 00 26 7e
2025-08-26 11:50:18.235188: 🔍 数据长度: 8 字节
2025-08-26 11:50:18.235188: 🔍 预定义命令列表:
2025-08-26 11:50:18.235188:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-26 11:50:18.235188:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-26 11:50:18.235188:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-26 11:50:18.235188:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-26 11:50:18.235188:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-26 11:50:18.235188:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-26 11:50:18.236188:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-26 11:50:18.236188:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-26 11:50:18.236188:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-26 11:50:18.236188:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-26 11:50:18.236188: ✅ 解析到闸机命令: GateCommand.exitEnd
2025-08-26 11:50:18.237188: 解析到闸机命令: exit_end (出馆结束)
2025-08-26 11:50:18.237188: 收到闸机命令: exit_end (出馆结束)
2025-08-26 11:50:18.237188: 出馆流程结束
2025-08-26 11:50:18.238187: 📊 流程状态已清除：进馆=false, 出馆=false
2025-08-26 11:50:18.238187: [channel_1] 收到闸机事件: exit_end
2025-08-26 11:50:18.238187: [channel_1] 主从机扩展：处理出馆结束
2025-08-26 11:50:18.238187: [channel_1] 清空处理队列，当前大小: 0
2025-08-26 11:50:18.238187: [channel_1] 处理队列已清空
2025-08-26 11:50:18.239187: 📨 收到GateCoordinator事件: exit_end
2025-08-26 11:50:18.239187: 页面状态变更: SilencePageState.welcome
2025-08-26 11:50:18.239187: [channel_1] 通知收集到的条码: []
2025-08-26 11:50:18.239187: ✅ [channel_1] 数据流通知发送成功: 0个条码
2025-08-26 11:50:18.591182: 🔄 开始RFID轮询检查...
2025-08-26 11:50:18.592186: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:18.592186: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:18.592186: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:18.593181: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:18.610180: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:18.610180:   - 设备句柄: 2395416852128
2025-08-26 11:50:18.611182:   - FetchRecords返回值: 0
2025-08-26 11:50:18.611182:   - 报告数量: 4
2025-08-26 11:50:18.611182:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:18.612181:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:18.612181:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:18.612181:   - 设备类型: LSGControlCenter
2025-08-26 11:50:18.612181:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:18.612181:   - 数据长度: 8
2025-08-26 11:50:18.613180:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:18.613180:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:18.613180:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:18.613180:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:18.613180:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:18.613180:   - 设备类型: LSGControlCenter
2025-08-26 11:50:18.614181:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:18.614181:   - 数据长度: 8
2025-08-26 11:50:18.614181:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:18.614181:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:18.614181:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:18.615180:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:18.615180:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:18.615180:   - 设备类型: LSGControlCenter
2025-08-26 11:50:18.615180:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:18.615180:   - 数据长度: 8
2025-08-26 11:50:18.615180:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:18.615180:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:18.615180:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:18.616180:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:18.616180:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:18.616180:   - 设备类型: LSGControlCenter
2025-08-26 11:50:18.616180:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:18.616180:   - 数据长度: 8
2025-08-26 11:50:18.616180:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:18.617181:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:18.617181: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:18.617181:   - 发现标签数量: 4
2025-08-26 11:50:18.617181:   - 标签详情:
2025-08-26 11:50:18.617181:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:18.617181:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:18.618180:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:18.618180:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:18.618180: RFID扫描: 发现 4 个标签
2025-08-26 11:50:18.618180: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:18.618180: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:18.618180: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:18.618180:   - UID: E004015305F83C8E
2025-08-26 11:50:18.619182:   - Data: E004015305F83C8E
2025-08-26 11:50:18.619182:   - EventType: 1
2025-08-26 11:50:18.619182:   - Direction: 0
2025-08-26 11:50:18.620186:   - Antenna: 1
2025-08-26 11:50:18.620186:   - TagFrequency: 0
2025-08-26 11:50:18.621185: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:18.621185:   - UID: E004015305F83C8E
2025-08-26 11:50:18.622181:   - Data: E004015305F83C8E
2025-08-26 11:50:18.622181:   - EventType: 1
2025-08-26 11:50:18.622181:   - Direction: 0
2025-08-26 11:50:18.622181:   - Antenna: 1
2025-08-26 11:50:18.622181:   - TagFrequency: 0
2025-08-26 11:50:18.623181: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:18.623181:   - UID: E004015305F83C8E
2025-08-26 11:50:18.623181:   - Data: E004015305F83C8E
2025-08-26 11:50:18.623181:   - EventType: 1
2025-08-26 11:50:18.623181:   - Direction: 0
2025-08-26 11:50:18.624180:   - Antenna: 1
2025-08-26 11:50:18.624180:   - TagFrequency: 0
2025-08-26 11:50:18.624180: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:18.624180:   - UID: E004015305F83C8E
2025-08-26 11:50:18.625180:   - Data: E004015305F83C8E
2025-08-26 11:50:18.625180:   - EventType: 1
2025-08-26 11:50:18.625180:   - Direction: 0
2025-08-26 11:50:18.625180:   - Antenna: 1
2025-08-26 11:50:18.626180:   - TagFrequency: 0
2025-08-26 11:50:19.091171: 🔄 开始RFID轮询检查...
2025-08-26 11:50:19.091171: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:19.092172: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:19.092172: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:19.092172: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:19.111172: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:19.112174:   - 设备句柄: 2395416852128
2025-08-26 11:50:19.113172:   - FetchRecords返回值: 0
2025-08-26 11:50:19.113172:   - 报告数量: 4
2025-08-26 11:50:19.114172:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:19.114172:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:19.114172:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:19.115172:   - 设备类型: LSGControlCenter
2025-08-26 11:50:19.115172:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:19.115172:   - 数据长度: 8
2025-08-26 11:50:19.115172:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:19.116171:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:19.116171:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:19.116171:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:19.116171:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:19.117171:   - 设备类型: LSGControlCenter
2025-08-26 11:50:19.117171:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:19.117171:   - 数据长度: 8
2025-08-26 11:50:19.117171:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:19.117171:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:19.118171:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:19.118171:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:19.118171:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:19.118171:   - 设备类型: LSGControlCenter
2025-08-26 11:50:19.118171:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:19.119171:   - 数据长度: 8
2025-08-26 11:50:19.119171:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:19.119171:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:19.119171:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:19.119171:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:19.119171:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:19.120171:   - 设备类型: LSGControlCenter
2025-08-26 11:50:19.120171:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:19.120171:   - 数据长度: 8
2025-08-26 11:50:19.120171:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:19.120171:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:19.120171: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:19.121171:   - 发现标签数量: 4
2025-08-26 11:50:19.121171:   - 标签详情:
2025-08-26 11:50:19.121171:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:19.121171:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:19.121171:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:19.121171:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:19.122171: RFID扫描: 发现 4 个标签
2025-08-26 11:50:19.122171: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:19.122171: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:19.122171: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:19.122171:   - UID: E004015305F83C8E
2025-08-26 11:50:19.122171:   - Data: E004015305F83C8E
2025-08-26 11:50:19.123171:   - EventType: 1
2025-08-26 11:50:19.123171:   - Direction: 0
2025-08-26 11:50:19.123171:   - Antenna: 1
2025-08-26 11:50:19.123171:   - TagFrequency: 0
2025-08-26 11:50:19.123171: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:19.123171:   - UID: E004015305F83C8E
2025-08-26 11:50:19.124171:   - Data: E004015305F83C8E
2025-08-26 11:50:19.124171:   - EventType: 1
2025-08-26 11:50:19.124171:   - Direction: 0
2025-08-26 11:50:19.124171:   - Antenna: 1
2025-08-26 11:50:19.124171:   - TagFrequency: 0
2025-08-26 11:50:19.124171: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:19.124171:   - UID: E004015305F83C8E
2025-08-26 11:50:19.125171:   - Data: E004015305F83C8E
2025-08-26 11:50:19.125171:   - EventType: 1
2025-08-26 11:50:19.125171:   - Direction: 0
2025-08-26 11:50:19.125171:   - Antenna: 1
2025-08-26 11:50:19.125171:   - TagFrequency: 0
2025-08-26 11:50:19.126171: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:19.126171:   - UID: E004015305F83C8E
2025-08-26 11:50:19.126171:   - Data: E004015305F83C8E
2025-08-26 11:50:19.126171:   - EventType: 1
2025-08-26 11:50:19.126171:   - Direction: 0
2025-08-26 11:50:19.126171:   - Antenna: 1
2025-08-26 11:50:19.126171:   - TagFrequency: 0
2025-08-26 11:50:19.591175: 🔄 开始RFID轮询检查...
2025-08-26 11:50:19.591175: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:19.592165: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:19.592165: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:19.593164: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:19.610162: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:19.610162:   - 设备句柄: 2395416852128
2025-08-26 11:50:19.611163:   - FetchRecords返回值: 0
2025-08-26 11:50:19.611163:   - 报告数量: 4
2025-08-26 11:50:19.611163:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:19.611163:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:19.611163:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:19.611163:   - 设备类型: LSGControlCenter
2025-08-26 11:50:19.611163:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:19.611163:   - 数据长度: 8
2025-08-26 11:50:19.612163:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:19.612163:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:19.612163:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:19.612163:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:19.612163:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:19.612163:   - 设备类型: LSGControlCenter
2025-08-26 11:50:19.612163:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:19.612163:   - 数据长度: 8
2025-08-26 11:50:19.613163:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:19.613163:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:19.613163:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:19.613163:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:19.613163:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:19.613163:   - 设备类型: LSGControlCenter
2025-08-26 11:50:19.613163:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:19.613163:   - 数据长度: 8
2025-08-26 11:50:19.614163:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:19.614163:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:19.614163:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:19.614163:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:19.614163:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:19.614163:   - 设备类型: LSGControlCenter
2025-08-26 11:50:19.614163:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:19.614163:   - 数据长度: 8
2025-08-26 11:50:19.615163:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:19.615163:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:19.615163: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:19.615163:   - 发现标签数量: 4
2025-08-26 11:50:19.615163:   - 标签详情:
2025-08-26 11:50:19.615163:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:19.615163:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:19.615163:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:19.616163:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:19.616163: RFID扫描: 发现 4 个标签
2025-08-26 11:50:19.616163: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:19.616163: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:19.616163: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:19.616163:   - UID: E004015305F83C8E
2025-08-26 11:50:19.616163:   - Data: E004015305F83C8E
2025-08-26 11:50:19.616163:   - EventType: 1
2025-08-26 11:50:19.617163:   - Direction: 0
2025-08-26 11:50:19.617163:   - Antenna: 1
2025-08-26 11:50:19.617163:   - TagFrequency: 0
2025-08-26 11:50:19.617163: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:19.617163:   - UID: E004015305F83C8E
2025-08-26 11:50:19.617163:   - Data: E004015305F83C8E
2025-08-26 11:50:19.617163:   - EventType: 1
2025-08-26 11:50:19.617163:   - Direction: 0
2025-08-26 11:50:19.618163:   - Antenna: 1
2025-08-26 11:50:19.618163:   - TagFrequency: 0
2025-08-26 11:50:19.618163: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:19.618163:   - UID: E004015305F83C8E
2025-08-26 11:50:19.618163:   - Data: E004015305F83C8E
2025-08-26 11:50:19.618163:   - EventType: 1
2025-08-26 11:50:19.618163:   - Direction: 0
2025-08-26 11:50:19.618163:   - Antenna: 1
2025-08-26 11:50:19.618163:   - TagFrequency: 0
2025-08-26 11:50:19.619163: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:19.619163:   - UID: E004015305F83C8E
2025-08-26 11:50:19.619163:   - Data: E004015305F83C8E
2025-08-26 11:50:19.619163:   - EventType: 1
2025-08-26 11:50:19.619163:   - Direction: 0
2025-08-26 11:50:19.619163:   - Antenna: 1
2025-08-26 11:50:19.619163:   - TagFrequency: 0
2025-08-26 11:50:20.091154: 🔄 开始RFID轮询检查...
2025-08-26 11:50:20.091154: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:20.091154: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:20.091154: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:20.091154: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:20.110155: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:20.111154:   - 设备句柄: 2395416852128
2025-08-26 11:50:20.111154:   - FetchRecords返回值: 0
2025-08-26 11:50:20.111154:   - 报告数量: 4
2025-08-26 11:50:20.111154:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:20.111154:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:20.111154:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:20.111154:   - 设备类型: LSGControlCenter
2025-08-26 11:50:20.112154:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:20.112154:   - 数据长度: 8
2025-08-26 11:50:20.112154:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:20.112154:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:20.112154:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:20.112154:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:20.112154:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:20.113154:   - 设备类型: LSGControlCenter
2025-08-26 11:50:20.113154:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:20.113154:   - 数据长度: 8
2025-08-26 11:50:20.113154:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:20.113154:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:20.113154:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:20.113154:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:20.114155:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:20.114155:   - 设备类型: LSGControlCenter
2025-08-26 11:50:20.114155:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:20.114155:   - 数据长度: 8
2025-08-26 11:50:20.114155:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:20.114155:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:20.114155:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:20.115154:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:20.115154:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:20.115154:   - 设备类型: LSGControlCenter
2025-08-26 11:50:20.115154:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:20.115154:   - 数据长度: 8
2025-08-26 11:50:20.115154:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:20.115154:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:20.116154: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:20.116154:   - 发现标签数量: 4
2025-08-26 11:50:20.116154:   - 标签详情:
2025-08-26 11:50:20.116154:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:20.116154:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:20.116154:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:20.116154:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:20.116154: RFID扫描: 发现 4 个标签
2025-08-26 11:50:20.117154: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:20.117154: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:20.117154: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:20.117154:   - UID: E004015305F83C8E
2025-08-26 11:50:20.117154:   - Data: E004015305F83C8E
2025-08-26 11:50:20.117154:   - EventType: 1
2025-08-26 11:50:20.117154:   - Direction: 0
2025-08-26 11:50:20.117154:   - Antenna: 1
2025-08-26 11:50:20.118154:   - TagFrequency: 0
2025-08-26 11:50:20.118154: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:20.118154:   - UID: E004015305F83C8E
2025-08-26 11:50:20.118154:   - Data: E004015305F83C8E
2025-08-26 11:50:20.118154:   - EventType: 1
2025-08-26 11:50:20.118154:   - Direction: 0
2025-08-26 11:50:20.118154:   - Antenna: 1
2025-08-26 11:50:20.118154:   - TagFrequency: 0
2025-08-26 11:50:20.118154: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:20.119154:   - UID: E004015305F83C8E
2025-08-26 11:50:20.119154:   - Data: E004015305F83C8E
2025-08-26 11:50:20.119154:   - EventType: 1
2025-08-26 11:50:20.119154:   - Direction: 0
2025-08-26 11:50:20.119154:   - Antenna: 1
2025-08-26 11:50:20.119154:   - TagFrequency: 0
2025-08-26 11:50:20.119154: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:20.120154:   - UID: E004015305F83C8E
2025-08-26 11:50:20.120154:   - Data: E004015305F83C8E
2025-08-26 11:50:20.120154:   - EventType: 1
2025-08-26 11:50:20.120154:   - Direction: 0
2025-08-26 11:50:20.120154:   - Antenna: 1
2025-08-26 11:50:20.120154:   - TagFrequency: 0
2025-08-26 11:50:20.591145: 🔄 开始RFID轮询检查...
2025-08-26 11:50:20.591145: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:20.591145: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:20.591145: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:20.591145: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:20.611146: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:20.611146:   - 设备句柄: 2395416852128
2025-08-26 11:50:20.612147:   - FetchRecords返回值: 0
2025-08-26 11:50:20.612147:   - 报告数量: 4
2025-08-26 11:50:20.612147:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:20.612147:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:20.613146:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:20.613146:   - 设备类型: LSGControlCenter
2025-08-26 11:50:20.613146:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:20.613146:   - 数据长度: 8
2025-08-26 11:50:20.613146:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:20.614145:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:20.614145:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:20.614145:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:20.614145:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:20.614145:   - 设备类型: LSGControlCenter
2025-08-26 11:50:20.614145:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:20.615145:   - 数据长度: 8
2025-08-26 11:50:20.615145:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:20.615145:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:20.615145:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:20.615145:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:20.616146:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:20.616146:   - 设备类型: LSGControlCenter
2025-08-26 11:50:20.616146:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:20.616146:   - 数据长度: 8
2025-08-26 11:50:20.616146:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:20.616146:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:20.616146:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:20.617145:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:20.617145:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:20.617145:   - 设备类型: LSGControlCenter
2025-08-26 11:50:20.617145:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:20.617145:   - 数据长度: 8
2025-08-26 11:50:20.617145:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:20.617145:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:20.618145: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:20.618145:   - 发现标签数量: 4
2025-08-26 11:50:20.618145:   - 标签详情:
2025-08-26 11:50:20.618145:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:20.619145:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:20.619145:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:20.619145:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:20.619145: RFID扫描: 发现 4 个标签
2025-08-26 11:50:20.620146: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:20.620146: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:20.620146: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:20.620146:   - UID: E004015305F83C8E
2025-08-26 11:50:20.620146:   - Data: E004015305F83C8E
2025-08-26 11:50:20.620146:   - EventType: 1
2025-08-26 11:50:20.621145:   - Direction: 0
2025-08-26 11:50:20.621145:   - Antenna: 1
2025-08-26 11:50:20.621145:   - TagFrequency: 0
2025-08-26 11:50:20.621145: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:20.621145:   - UID: E004015305F83C8E
2025-08-26 11:50:20.621145:   - Data: E004015305F83C8E
2025-08-26 11:50:20.622145:   - EventType: 1
2025-08-26 11:50:20.622145:   - Direction: 0
2025-08-26 11:50:20.622145:   - Antenna: 1
2025-08-26 11:50:20.622145:   - TagFrequency: 0
2025-08-26 11:50:20.622145: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:20.622145:   - UID: E004015305F83C8E
2025-08-26 11:50:20.622145:   - Data: E004015305F83C8E
2025-08-26 11:50:20.622145:   - EventType: 1
2025-08-26 11:50:20.623145:   - Direction: 0
2025-08-26 11:50:20.623145:   - Antenna: 1
2025-08-26 11:50:20.623145:   - TagFrequency: 0
2025-08-26 11:50:20.623145: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:20.623145:   - UID: E004015305F83C8E
2025-08-26 11:50:20.623145:   - Data: E004015305F83C8E
2025-08-26 11:50:20.623145:   - EventType: 1
2025-08-26 11:50:20.624145:   - Direction: 0
2025-08-26 11:50:20.624145:   - Antenna: 1
2025-08-26 11:50:20.624145:   - TagFrequency: 0
2025-08-26 11:50:21.090137: 🔄 开始RFID轮询检查...
2025-08-26 11:50:21.090137: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:21.090137: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:21.090137: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:21.090137: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:21.111137: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:21.111137:   - 设备句柄: 2395416852128
2025-08-26 11:50:21.111137:   - FetchRecords返回值: 0
2025-08-26 11:50:21.112137:   - 报告数量: 4
2025-08-26 11:50:21.112137:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:21.112137:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:21.113137:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:21.113137:   - 设备类型: LSGControlCenter
2025-08-26 11:50:21.113137:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:21.113137:   - 数据长度: 8
2025-08-26 11:50:21.113137:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:21.114138:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:21.114138:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:21.114138:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:21.114138:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:21.114138:   - 设备类型: LSGControlCenter
2025-08-26 11:50:21.115137:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:21.115137:   - 数据长度: 8
2025-08-26 11:50:21.115137:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:21.115137:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:21.116137:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:21.116137:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:21.116137:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:21.117138:   - 设备类型: LSGControlCenter
2025-08-26 11:50:21.117138:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:21.117138:   - 数据长度: 8
2025-08-26 11:50:21.118143:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:21.118143:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:21.119138:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:21.119138:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:21.119138:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:21.120138:   - 设备类型: LSGControlCenter
2025-08-26 11:50:21.120138:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:21.120138:   - 数据长度: 8
2025-08-26 11:50:21.120138:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:21.121136:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:21.121136: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:21.121136:   - 发现标签数量: 4
2025-08-26 11:50:21.121136:   - 标签详情:
2025-08-26 11:50:21.121136:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:21.122136:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:21.122136:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:21.122136:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:21.122136: RFID扫描: 发现 4 个标签
2025-08-26 11:50:21.122136: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:21.122136: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:21.123136: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:21.123136:   - UID: E004015305F83C8E
2025-08-26 11:50:21.123136:   - Data: E004015305F83C8E
2025-08-26 11:50:21.123136:   - EventType: 1
2025-08-26 11:50:21.123136:   - Direction: 0
2025-08-26 11:50:21.124136:   - Antenna: 1
2025-08-26 11:50:21.124136:   - TagFrequency: 0
2025-08-26 11:50:21.124136: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:21.124136:   - UID: E004015305F83C8E
2025-08-26 11:50:21.124136:   - Data: E004015305F83C8E
2025-08-26 11:50:21.124136:   - EventType: 1
2025-08-26 11:50:21.124136:   - Direction: 0
2025-08-26 11:50:21.125136:   - Antenna: 1
2025-08-26 11:50:21.125136:   - TagFrequency: 0
2025-08-26 11:50:21.125136: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:21.125136:   - UID: E004015305F83C8E
2025-08-26 11:50:21.125136:   - Data: E004015305F83C8E
2025-08-26 11:50:21.125136:   - EventType: 1
2025-08-26 11:50:21.125136:   - Direction: 0
2025-08-26 11:50:21.126136:   - Antenna: 1
2025-08-26 11:50:21.126136:   - TagFrequency: 0
2025-08-26 11:50:21.126136: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:21.126136:   - UID: E004015305F83C8E
2025-08-26 11:50:21.126136:   - Data: E004015305F83C8E
2025-08-26 11:50:21.126136:   - EventType: 1
2025-08-26 11:50:21.126136:   - Direction: 0
2025-08-26 11:50:21.127136:   - Antenna: 1
2025-08-26 11:50:21.127136:   - TagFrequency: 0
2025-08-26 11:50:21.591128: 🔄 开始RFID轮询检查...
2025-08-26 11:50:21.591128: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:21.591128: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:21.591128: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:21.591128: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:21.610128: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:21.610128:   - 设备句柄: 2395416852128
2025-08-26 11:50:21.610128:   - FetchRecords返回值: 0
2025-08-26 11:50:21.611129:   - 报告数量: 4
2025-08-26 11:50:21.611129:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:21.611129:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:21.611129:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:21.611129:   - 设备类型: LSGControlCenter
2025-08-26 11:50:21.611129:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:21.611129:   - 数据长度: 8
2025-08-26 11:50:21.612128:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:21.612128:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:21.612128:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:21.612128:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:21.612128:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:21.612128:   - 设备类型: LSGControlCenter
2025-08-26 11:50:21.612128:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:21.613128:   - 数据长度: 8
2025-08-26 11:50:21.613128:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:21.613128:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:21.613128:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:21.613128:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:21.614129:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:21.614129:   - 设备类型: LSGControlCenter
2025-08-26 11:50:21.614129:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:21.615131:   - 数据长度: 8
2025-08-26 11:50:21.615131:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:21.615131:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:21.616129:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:21.616129:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:21.616129:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:21.617128:   - 设备类型: LSGControlCenter
2025-08-26 11:50:21.617128:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:21.617128:   - 数据长度: 8
2025-08-26 11:50:21.617128:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:21.617128:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:21.618128: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:21.618128:   - 发现标签数量: 4
2025-08-26 11:50:21.618128:   - 标签详情:
2025-08-26 11:50:21.618128:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:21.618128:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:21.619128:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:21.619128:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:21.619128: RFID扫描: 发现 4 个标签
2025-08-26 11:50:21.619128: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:21.619128: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:21.619128: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:21.620128:   - UID: E004015305F83C8E
2025-08-26 11:50:21.620128:   - Data: E004015305F83C8E
2025-08-26 11:50:21.620128:   - EventType: 1
2025-08-26 11:50:21.620128:   - Direction: 0
2025-08-26 11:50:21.620128:   - Antenna: 1
2025-08-26 11:50:21.620128:   - TagFrequency: 0
2025-08-26 11:50:21.621128: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:21.621128:   - UID: E004015305F83C8E
2025-08-26 11:50:21.621128:   - Data: E004015305F83C8E
2025-08-26 11:50:21.621128:   - EventType: 1
2025-08-26 11:50:21.621128:   - Direction: 0
2025-08-26 11:50:21.621128:   - Antenna: 1
2025-08-26 11:50:21.621128:   - TagFrequency: 0
2025-08-26 11:50:21.622128: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:21.622128:   - UID: E004015305F83C8E
2025-08-26 11:50:21.622128:   - Data: E004015305F83C8E
2025-08-26 11:50:21.622128:   - EventType: 1
2025-08-26 11:50:21.622128:   - Direction: 0
2025-08-26 11:50:21.622128:   - Antenna: 1
2025-08-26 11:50:21.623133:   - TagFrequency: 0
2025-08-26 11:50:21.623133: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:21.623133:   - UID: E004015305F83C8E
2025-08-26 11:50:21.623133:   - Data: E004015305F83C8E
2025-08-26 11:50:21.624128:   - EventType: 1
2025-08-26 11:50:21.624128:   - Direction: 0
2025-08-26 11:50:21.624128:   - Antenna: 1
2025-08-26 11:50:21.624128:   - TagFrequency: 0
2025-08-26 11:50:22.090120: 🔄 开始RFID轮询检查...
2025-08-26 11:50:22.091124: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:22.092120: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:22.092120: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:22.092120: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:22.111119: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:22.111119:   - 设备句柄: 2395416852128
2025-08-26 11:50:22.111119:   - FetchRecords返回值: 0
2025-08-26 11:50:22.111119:   - 报告数量: 4
2025-08-26 11:50:22.111119:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:22.112119:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:22.112119:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:22.112119:   - 设备类型: LSGControlCenter
2025-08-26 11:50:22.112119:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:22.112119:   - 数据长度: 8
2025-08-26 11:50:22.112119:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:22.113119:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:22.113119:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:22.113119:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:22.113119:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:22.113119:   - 设备类型: LSGControlCenter
2025-08-26 11:50:22.113119:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:22.114119:   - 数据长度: 8
2025-08-26 11:50:22.114119:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:22.114119:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:22.114119:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:22.114119:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:22.114119:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:22.114119:   - 设备类型: LSGControlCenter
2025-08-26 11:50:22.114119:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:22.115119:   - 数据长度: 8
2025-08-26 11:50:22.115119:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:22.115119:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:22.115119:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:22.115119:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:22.115119:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:22.116120:   - 设备类型: LSGControlCenter
2025-08-26 11:50:22.116120:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:22.116120:   - 数据长度: 8
2025-08-26 11:50:22.117126:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:22.117126:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:22.117126: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:22.117126:   - 发现标签数量: 4
2025-08-26 11:50:22.117126:   - 标签详情:
2025-08-26 11:50:22.118124:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:22.118124:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:22.118124:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:22.118124:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:22.118124: RFID扫描: 发现 4 个标签
2025-08-26 11:50:22.118124: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:22.119120: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:22.119120: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:22.119120:   - UID: E004015305F83C8E
2025-08-26 11:50:22.119120:   - Data: E004015305F83C8E
2025-08-26 11:50:22.119120:   - EventType: 1
2025-08-26 11:50:22.119120:   - Direction: 0
2025-08-26 11:50:22.120119:   - Antenna: 1
2025-08-26 11:50:22.120119:   - TagFrequency: 0
2025-08-26 11:50:22.120119: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:22.120119:   - UID: E004015305F83C8E
2025-08-26 11:50:22.120119:   - Data: E004015305F83C8E
2025-08-26 11:50:22.120119:   - EventType: 1
2025-08-26 11:50:22.120119:   - Direction: 0
2025-08-26 11:50:22.120119:   - Antenna: 1
2025-08-26 11:50:22.121119:   - TagFrequency: 0
2025-08-26 11:50:22.121119: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:22.121119:   - UID: E004015305F83C8E
2025-08-26 11:50:22.121119:   - Data: E004015305F83C8E
2025-08-26 11:50:22.121119:   - EventType: 1
2025-08-26 11:50:22.121119:   - Direction: 0
2025-08-26 11:50:22.121119:   - Antenna: 1
2025-08-26 11:50:22.122120:   - TagFrequency: 0
2025-08-26 11:50:22.122120: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:22.122120:   - UID: E004015305F83C8E
2025-08-26 11:50:22.122120:   - Data: E004015305F83C8E
2025-08-26 11:50:22.122120:   - EventType: 1
2025-08-26 11:50:22.122120:   - Direction: 0
2025-08-26 11:50:22.123120:   - Antenna: 1
2025-08-26 11:50:22.123120:   - TagFrequency: 0
2025-08-26 11:50:22.591127: 🔄 开始RFID轮询检查...
2025-08-26 11:50:22.592113: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:22.593117: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:22.593117: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:22.594112: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:22.611111: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:22.612112:   - 设备句柄: 2395416852128
2025-08-26 11:50:22.612112:   - FetchRecords返回值: 0
2025-08-26 11:50:22.612112:   - 报告数量: 4
2025-08-26 11:50:22.612112:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:22.613111:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:22.613111:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:22.613111:   - 设备类型: LSGControlCenter
2025-08-26 11:50:22.613111:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:22.613111:   - 数据长度: 8
2025-08-26 11:50:22.613111:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:22.613111:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:22.614111:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:22.614111:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:22.614111:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:22.614111:   - 设备类型: LSGControlCenter
2025-08-26 11:50:22.614111:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:22.614111:   - 数据长度: 8
2025-08-26 11:50:22.614111:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:22.615111:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:22.615111:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:22.615111:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:22.615111:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:22.615111:   - 设备类型: LSGControlCenter
2025-08-26 11:50:22.616111:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:22.616111:   - 数据长度: 8
2025-08-26 11:50:22.616111:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:22.616111:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:22.616111:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:22.616111:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:22.616111:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:22.617111:   - 设备类型: LSGControlCenter
2025-08-26 11:50:22.617111:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:22.617111:   - 数据长度: 8
2025-08-26 11:50:22.617111:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:22.617111:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:22.617111: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:22.617111:   - 发现标签数量: 4
2025-08-26 11:50:22.617111:   - 标签详情:
2025-08-26 11:50:22.618110:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:22.618110:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:22.618110:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:22.618110:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:22.618110: RFID扫描: 发现 4 个标签
2025-08-26 11:50:22.618110: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:22.618110: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:22.619110: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:22.619110:   - UID: E004015305F83C8E
2025-08-26 11:50:22.619110:   - Data: E004015305F83C8E
2025-08-26 11:50:22.619110:   - EventType: 1
2025-08-26 11:50:22.619110:   - Direction: 0
2025-08-26 11:50:22.619110:   - Antenna: 1
2025-08-26 11:50:22.619110:   - TagFrequency: 0
2025-08-26 11:50:22.619110: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:22.620110:   - UID: E004015305F83C8E
2025-08-26 11:50:22.620110:   - Data: E004015305F83C8E
2025-08-26 11:50:22.620110:   - EventType: 1
2025-08-26 11:50:22.620110:   - Direction: 0
2025-08-26 11:50:22.620110:   - Antenna: 1
2025-08-26 11:50:22.620110:   - TagFrequency: 0
2025-08-26 11:50:22.620110: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:22.620110:   - UID: E004015305F83C8E
2025-08-26 11:50:22.620110:   - Data: E004015305F83C8E
2025-08-26 11:50:22.621110:   - EventType: 1
2025-08-26 11:50:22.621110:   - Direction: 0
2025-08-26 11:50:22.621110:   - Antenna: 1
2025-08-26 11:50:22.621110:   - TagFrequency: 0
2025-08-26 11:50:22.621110: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:22.621110:   - UID: E004015305F83C8E
2025-08-26 11:50:22.621110:   - Data: E004015305F83C8E
2025-08-26 11:50:22.621110:   - EventType: 1
2025-08-26 11:50:22.622110:   - Direction: 0
2025-08-26 11:50:22.622110:   - Antenna: 1
2025-08-26 11:50:22.622110:   - TagFrequency: 0
2025-08-26 11:50:23.091102: 🔄 开始RFID轮询检查...
2025-08-26 11:50:23.091102: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:23.091102: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:23.092103: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:23.092103: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:23.111102: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:23.111102:   - 设备句柄: 2395416852128
2025-08-26 11:50:23.111102:   - FetchRecords返回值: 0
2025-08-26 11:50:23.111102:   - 报告数量: 4
2025-08-26 11:50:23.111102:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:23.112102:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:23.112102:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:23.112102:   - 设备类型: LSGControlCenter
2025-08-26 11:50:23.112102:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:23.112102:   - 数据长度: 8
2025-08-26 11:50:23.112102:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:23.112102:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:23.113102:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:23.113102:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:23.113102:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:23.113102:   - 设备类型: LSGControlCenter
2025-08-26 11:50:23.113102:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:23.113102:   - 数据长度: 8
2025-08-26 11:50:23.113102:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:23.113102:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:23.114102:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:23.114102:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:23.114102:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:23.114102:   - 设备类型: LSGControlCenter
2025-08-26 11:50:23.114102:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:23.114102:   - 数据长度: 8
2025-08-26 11:50:23.114102:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:23.115102:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:23.115102:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:23.115102:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:23.115102:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:23.115102:   - 设备类型: LSGControlCenter
2025-08-26 11:50:23.115102:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:23.115102:   - 数据长度: 8
2025-08-26 11:50:23.115102:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:23.116102:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:23.116102: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:23.116102:   - 发现标签数量: 4
2025-08-26 11:50:23.116102:   - 标签详情:
2025-08-26 11:50:23.116102:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:23.116102:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:23.116102:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:23.116102:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:23.117102: RFID扫描: 发现 4 个标签
2025-08-26 11:50:23.117102: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:23.117102: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:23.117102: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:23.117102:   - UID: E004015305F83C8E
2025-08-26 11:50:23.117102:   - Data: E004015305F83C8E
2025-08-26 11:50:23.118111:   - EventType: 1
2025-08-26 11:50:23.118111:   - Direction: 0
2025-08-26 11:50:23.118111:   - Antenna: 1
2025-08-26 11:50:23.118111:   - TagFrequency: 0
2025-08-26 11:50:23.119104: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:23.119104:   - UID: E004015305F83C8E
2025-08-26 11:50:23.119104:   - Data: E004015305F83C8E
2025-08-26 11:50:23.119104:   - EventType: 1
2025-08-26 11:50:23.119104:   - Direction: 0
2025-08-26 11:50:23.119104:   - Antenna: 1
2025-08-26 11:50:23.120102:   - TagFrequency: 0
2025-08-26 11:50:23.120102: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:23.120102:   - UID: E004015305F83C8E
2025-08-26 11:50:23.120102:   - Data: E004015305F83C8E
2025-08-26 11:50:23.121102:   - EventType: 1
2025-08-26 11:50:23.121102:   - Direction: 0
2025-08-26 11:50:23.121102:   - Antenna: 1
2025-08-26 11:50:23.122102:   - TagFrequency: 0
2025-08-26 11:50:23.122102: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:23.122102:   - UID: E004015305F83C8E
2025-08-26 11:50:23.122102:   - Data: E004015305F83C8E
2025-08-26 11:50:23.123102:   - EventType: 1
2025-08-26 11:50:23.123102:   - Direction: 0
2025-08-26 11:50:23.123102:   - Antenna: 1
2025-08-26 11:50:23.123102:   - TagFrequency: 0
2025-08-26 11:50:23.591094: 🔄 开始RFID轮询检查...
2025-08-26 11:50:23.591094: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:23.591094: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:23.591094: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:23.592093: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:23.611094: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:23.612096:   - 设备句柄: 2395416852128
2025-08-26 11:50:23.612096:   - FetchRecords返回值: 0
2025-08-26 11:50:23.612096:   - 报告数量: 4
2025-08-26 11:50:23.613094:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:23.613094:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:23.613094:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:23.614093:   - 设备类型: LSGControlCenter
2025-08-26 11:50:23.614093:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:23.614093:   - 数据长度: 8
2025-08-26 11:50:23.614093:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:23.614093:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:23.615093:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:23.615093:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:23.615093:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:23.615093:   - 设备类型: LSGControlCenter
2025-08-26 11:50:23.616096:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:23.616096:   - 数据长度: 8
2025-08-26 11:50:23.616096:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:23.616096:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:23.617093:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:23.617093:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:23.617093:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:23.617093:   - 设备类型: LSGControlCenter
2025-08-26 11:50:23.618093:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:23.618093:   - 数据长度: 8
2025-08-26 11:50:23.618093:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:23.618093:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:23.619093:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:23.619093:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:23.619093:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:23.619093:   - 设备类型: LSGControlCenter
2025-08-26 11:50:23.619093:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:23.620093:   - 数据长度: 8
2025-08-26 11:50:23.620093:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:23.620093:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:23.620093: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:23.620093:   - 发现标签数量: 4
2025-08-26 11:50:23.621093:   - 标签详情:
2025-08-26 11:50:23.621093:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:23.621093:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:23.621093:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:23.622094:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:23.622094: RFID扫描: 发现 4 个标签
2025-08-26 11:50:23.622094: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:23.622094: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:23.623093: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:23.623093:   - UID: E004015305F83C8E
2025-08-26 11:50:23.623093:   - Data: E004015305F83C8E
2025-08-26 11:50:23.623093:   - EventType: 1
2025-08-26 11:50:23.623093:   - Direction: 0
2025-08-26 11:50:23.623093:   - Antenna: 1
2025-08-26 11:50:23.624093:   - TagFrequency: 0
2025-08-26 11:50:23.624093: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:23.624093:   - UID: E004015305F83C8E
2025-08-26 11:50:23.624093:   - Data: E004015305F83C8E
2025-08-26 11:50:23.624093:   - EventType: 1
2025-08-26 11:50:23.625093:   - Direction: 0
2025-08-26 11:50:23.625093:   - Antenna: 1
2025-08-26 11:50:23.626096:   - TagFrequency: 0
2025-08-26 11:50:23.626096: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:23.627094:   - UID: E004015305F83C8E
2025-08-26 11:50:23.627094:   - Data: E004015305F83C8E
2025-08-26 11:50:23.627094:   - EventType: 1
2025-08-26 11:50:23.627094:   - Direction: 0
2025-08-26 11:50:23.628110:   - Antenna: 1
2025-08-26 11:50:23.628110:   - TagFrequency: 0
2025-08-26 11:50:23.628110: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:23.628110:   - UID: E004015305F83C8E
2025-08-26 11:50:23.629093:   - Data: E004015305F83C8E
2025-08-26 11:50:23.629093:   - EventType: 1
2025-08-26 11:50:23.629093:   - Direction: 0
2025-08-26 11:50:23.629093:   - Antenna: 1
2025-08-26 11:50:23.629093:   - TagFrequency: 0
2025-08-26 11:50:24.091086: 🔄 开始RFID轮询检查...
2025-08-26 11:50:24.091086: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:24.092086: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:24.092086: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:24.092086: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:24.110084: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:24.110084:   - 设备句柄: 2395416852128
2025-08-26 11:50:24.110084:   - FetchRecords返回值: 0
2025-08-26 11:50:24.110084:   - 报告数量: 4
2025-08-26 11:50:24.110084:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:24.111084:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:24.111084:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:24.111084:   - 设备类型: LSGControlCenter
2025-08-26 11:50:24.111084:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:24.111084:   - 数据长度: 8
2025-08-26 11:50:24.111084:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:24.111084:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:24.112085:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:24.112085:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:24.112085:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:24.112085:   - 设备类型: LSGControlCenter
2025-08-26 11:50:24.112085:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:24.112085:   - 数据长度: 8
2025-08-26 11:50:24.112085:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:24.112085:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:24.113084:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:24.113084:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:24.113084:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:24.113084:   - 设备类型: LSGControlCenter
2025-08-26 11:50:24.113084:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:24.113084:   - 数据长度: 8
2025-08-26 11:50:24.113084:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:24.113084:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:24.114085:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:24.114085:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:24.114085:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:24.114085:   - 设备类型: LSGControlCenter
2025-08-26 11:50:24.114085:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:24.114085:   - 数据长度: 8
2025-08-26 11:50:24.114085:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:24.114085:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:24.115084: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:24.115084:   - 发现标签数量: 4
2025-08-26 11:50:24.115084:   - 标签详情:
2025-08-26 11:50:24.115084:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:24.115084:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:24.115084:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:24.115084:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:24.115084: RFID扫描: 发现 4 个标签
2025-08-26 11:50:24.116084: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:24.116084: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:24.116084: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:24.116084:   - UID: E004015305F83C8E
2025-08-26 11:50:24.116084:   - Data: E004015305F83C8E
2025-08-26 11:50:24.116084:   - EventType: 1
2025-08-26 11:50:24.116084:   - Direction: 0
2025-08-26 11:50:24.116084:   - Antenna: 1
2025-08-26 11:50:24.117084:   - TagFrequency: 0
2025-08-26 11:50:24.117084: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:24.117084:   - UID: E004015305F83C8E
2025-08-26 11:50:24.117084:   - Data: E004015305F83C8E
2025-08-26 11:50:24.117084:   - EventType: 1
2025-08-26 11:50:24.117084:   - Direction: 0
2025-08-26 11:50:24.117084:   - Antenna: 1
2025-08-26 11:50:24.117084:   - TagFrequency: 0
2025-08-26 11:50:24.117084: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:24.118084:   - UID: E004015305F83C8E
2025-08-26 11:50:24.118084:   - Data: E004015305F83C8E
2025-08-26 11:50:24.118084:   - EventType: 1
2025-08-26 11:50:24.118084:   - Direction: 0
2025-08-26 11:50:24.118084:   - Antenna: 1
2025-08-26 11:50:24.118084:   - TagFrequency: 0
2025-08-26 11:50:24.118084: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:24.118084:   - UID: E004015305F83C8E
2025-08-26 11:50:24.119084:   - Data: E004015305F83C8E
2025-08-26 11:50:24.119084:   - EventType: 1
2025-08-26 11:50:24.119084:   - Direction: 0
2025-08-26 11:50:24.119084:   - Antenna: 1
2025-08-26 11:50:24.119084:   - TagFrequency: 0
2025-08-26 11:50:24.591076: 🔄 开始RFID轮询检查...
2025-08-26 11:50:24.591076: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:24.591076: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:24.591076: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:24.591076: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:24.611075: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:24.611075:   - 设备句柄: 2395416852128
2025-08-26 11:50:24.611075:   - FetchRecords返回值: 0
2025-08-26 11:50:24.611075:   - 报告数量: 4
2025-08-26 11:50:24.612076:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:24.612076:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:24.612076:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:24.612076:   - 设备类型: LSGControlCenter
2025-08-26 11:50:24.612076:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:24.612076:   - 数据长度: 8
2025-08-26 11:50:24.613076:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:24.613076:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:24.614077:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:24.614077:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:24.614077:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:24.615076:   - 设备类型: LSGControlCenter
2025-08-26 11:50:24.615076:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:24.615076:   - 数据长度: 8
2025-08-26 11:50:24.616081:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:24.616081:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:24.616081:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:24.617077:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:24.617077:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:24.617077:   - 设备类型: LSGControlCenter
2025-08-26 11:50:24.617077:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:24.618076:   - 数据长度: 8
2025-08-26 11:50:24.618076:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:24.618076:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:24.618076:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:24.619076:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:24.619076:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:24.619076:   - 设备类型: LSGControlCenter
2025-08-26 11:50:24.620076:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:24.620076:   - 数据长度: 8
2025-08-26 11:50:24.620076:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:24.620076:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:24.620076: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:24.621077:   - 发现标签数量: 4
2025-08-26 11:50:24.621077:   - 标签详情:
2025-08-26 11:50:24.621077:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:24.621077:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:24.622076:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:24.622076:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:24.622076: RFID扫描: 发现 4 个标签
2025-08-26 11:50:24.622076: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:24.622076: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:24.623076: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:24.623076:   - UID: E004015305F83C8E
2025-08-26 11:50:24.623076:   - Data: E004015305F83C8E
2025-08-26 11:50:24.623076:   - EventType: 1
2025-08-26 11:50:24.623076:   - Direction: 0
2025-08-26 11:50:24.623076:   - Antenna: 1
2025-08-26 11:50:24.623076:   - TagFrequency: 0
2025-08-26 11:50:24.624076: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:24.624076:   - UID: E004015305F83C8E
2025-08-26 11:50:24.624076:   - Data: E004015305F83C8E
2025-08-26 11:50:24.624076:   - EventType: 1
2025-08-26 11:50:24.624076:   - Direction: 0
2025-08-26 11:50:24.624076:   - Antenna: 1
2025-08-26 11:50:24.624076:   - TagFrequency: 0
2025-08-26 11:50:24.624076: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:24.625076:   - UID: E004015305F83C8E
2025-08-26 11:50:24.625076:   - Data: E004015305F83C8E
2025-08-26 11:50:24.626076:   - EventType: 1
2025-08-26 11:50:24.626076:   - Direction: 0
2025-08-26 11:50:24.626076:   - Antenna: 1
2025-08-26 11:50:24.626076:   - TagFrequency: 0
2025-08-26 11:50:24.627075: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:24.627075:   - UID: E004015305F83C8E
2025-08-26 11:50:24.627075:   - Data: E004015305F83C8E
2025-08-26 11:50:24.627075:   - EventType: 1
2025-08-26 11:50:24.627075:   - Direction: 0
2025-08-26 11:50:24.627075:   - Antenna: 1
2025-08-26 11:50:24.628077:   - TagFrequency: 0
2025-08-26 11:50:25.090068: 🔄 开始RFID轮询检查...
2025-08-26 11:50:25.090068: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:25.090068: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:25.090068: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:25.091068: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:25.111067: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:25.111067:   - 设备句柄: 2395416852128
2025-08-26 11:50:25.111067:   - FetchRecords返回值: 0
2025-08-26 11:50:25.111067:   - 报告数量: 4
2025-08-26 11:50:25.112067:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:25.112067:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:25.112067:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:25.112067:   - 设备类型: LSGControlCenter
2025-08-26 11:50:25.112067:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:25.112067:   - 数据长度: 8
2025-08-26 11:50:25.112067:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:25.112067:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:25.113067:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:25.113067:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:25.113067:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:25.113067:   - 设备类型: LSGControlCenter
2025-08-26 11:50:25.113067:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:25.113067:   - 数据长度: 8
2025-08-26 11:50:25.113067:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:25.114067:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:25.114067:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:25.114067:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:25.114067:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:25.114067:   - 设备类型: LSGControlCenter
2025-08-26 11:50:25.114067:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:25.114067:   - 数据长度: 8
2025-08-26 11:50:25.114067:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:25.115067:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:25.115067:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:25.115067:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:25.115067:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:25.115067:   - 设备类型: LSGControlCenter
2025-08-26 11:50:25.115067:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:25.115067:   - 数据长度: 8
2025-08-26 11:50:25.115067:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:25.116069:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:25.116069: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:25.116069:   - 发现标签数量: 4
2025-08-26 11:50:25.116069:   - 标签详情:
2025-08-26 11:50:25.116069:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:25.116069:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:25.116069:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:25.116069:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:25.117067: RFID扫描: 发现 4 个标签
2025-08-26 11:50:25.117067: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:25.117067: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:25.117067: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:25.117067:   - UID: E004015305F83C8E
2025-08-26 11:50:25.117067:   - Data: E004015305F83C8E
2025-08-26 11:50:25.117067:   - EventType: 1
2025-08-26 11:50:25.117067:   - Direction: 0
2025-08-26 11:50:25.117067:   - Antenna: 1
2025-08-26 11:50:25.118067:   - TagFrequency: 0
2025-08-26 11:50:25.118067: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:25.118067:   - UID: E004015305F83C8E
2025-08-26 11:50:25.118067:   - Data: E004015305F83C8E
2025-08-26 11:50:25.118067:   - EventType: 1
2025-08-26 11:50:25.118067:   - Direction: 0
2025-08-26 11:50:25.118067:   - Antenna: 1
2025-08-26 11:50:25.118067:   - TagFrequency: 0
2025-08-26 11:50:25.119067: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:25.119067:   - UID: E004015305F83C8E
2025-08-26 11:50:25.119067:   - Data: E004015305F83C8E
2025-08-26 11:50:25.119067:   - EventType: 1
2025-08-26 11:50:25.119067:   - Direction: 0
2025-08-26 11:50:25.119067:   - Antenna: 1
2025-08-26 11:50:25.119067:   - TagFrequency: 0
2025-08-26 11:50:25.119067: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:25.120067:   - UID: E004015305F83C8E
2025-08-26 11:50:25.120067:   - Data: E004015305F83C8E
2025-08-26 11:50:25.120067:   - EventType: 1
2025-08-26 11:50:25.120067:   - Direction: 0
2025-08-26 11:50:25.120067:   - Antenna: 1
2025-08-26 11:50:25.120067:   - TagFrequency: 0
2025-08-26 11:50:25.591058: 🔄 开始RFID轮询检查...
2025-08-26 11:50:25.591058: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:25.591058: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:25.591058: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:25.591058: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:25.610059: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:25.611060:   - 设备句柄: 2395416852128
2025-08-26 11:50:25.611060:   - FetchRecords返回值: 0
2025-08-26 11:50:25.611060:   - 报告数量: 4
2025-08-26 11:50:25.612059:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:25.612059:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:25.612059:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:25.612059:   - 设备类型: LSGControlCenter
2025-08-26 11:50:25.612059:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:25.613059:   - 数据长度: 8
2025-08-26 11:50:25.613059:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:25.613059:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:25.613059:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:25.613059:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:25.614059:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:25.614059:   - 设备类型: LSGControlCenter
2025-08-26 11:50:25.614059:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:25.614059:   - 数据长度: 8
2025-08-26 11:50:25.614059:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:25.614059:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:25.615058:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:25.615058:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:25.615058:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:25.615058:   - 设备类型: LSGControlCenter
2025-08-26 11:50:25.615058:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:25.615058:   - 数据长度: 8
2025-08-26 11:50:25.615058:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:25.615058:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:25.616058:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:25.616058:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:25.616058:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:25.616058:   - 设备类型: LSGControlCenter
2025-08-26 11:50:25.616058:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:25.616058:   - 数据长度: 8
2025-08-26 11:50:25.616058:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:25.617058:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:25.617058: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:25.617058:   - 发现标签数量: 4
2025-08-26 11:50:25.617058:   - 标签详情:
2025-08-26 11:50:25.617058:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:25.617058:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:25.617058:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:25.617058:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:25.618058: RFID扫描: 发现 4 个标签
2025-08-26 11:50:25.618058: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:25.618058: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:25.618058: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:25.618058:   - UID: E004015305F83C8E
2025-08-26 11:50:25.618058:   - Data: E004015305F83C8E
2025-08-26 11:50:25.619058:   - EventType: 1
2025-08-26 11:50:25.619058:   - Direction: 0
2025-08-26 11:50:25.619058:   - Antenna: 1
2025-08-26 11:50:25.620059:   - TagFrequency: 0
2025-08-26 11:50:25.621064: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:25.621064:   - UID: E004015305F83C8E
2025-08-26 11:50:25.621064:   - Data: E004015305F83C8E
2025-08-26 11:50:25.622059:   - EventType: 1
2025-08-26 11:50:25.622059:   - Direction: 0
2025-08-26 11:50:25.622059:   - Antenna: 1
2025-08-26 11:50:25.622059:   - TagFrequency: 0
2025-08-26 11:50:25.623059: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:25.623059:   - UID: E004015305F83C8E
2025-08-26 11:50:25.623059:   - Data: E004015305F83C8E
2025-08-26 11:50:25.624058:   - EventType: 1
2025-08-26 11:50:25.624058:   - Direction: 0
2025-08-26 11:50:25.624058:   - Antenna: 1
2025-08-26 11:50:25.624058:   - TagFrequency: 0
2025-08-26 11:50:25.625058: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:25.625058:   - UID: E004015305F83C8E
2025-08-26 11:50:25.625058:   - Data: E004015305F83C8E
2025-08-26 11:50:25.625058:   - EventType: 1
2025-08-26 11:50:25.626058:   - Direction: 0
2025-08-26 11:50:25.626058:   - Antenna: 1
2025-08-26 11:50:25.626058:   - TagFrequency: 0
2025-08-26 11:50:26.090050: 🔄 开始RFID轮询检查...
2025-08-26 11:50:26.091058: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:26.092051: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:26.092051: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:26.092051: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:26.110050: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:26.110050:   - 设备句柄: 2395416852128
2025-08-26 11:50:26.110050:   - FetchRecords返回值: 0
2025-08-26 11:50:26.110050:   - 报告数量: 4
2025-08-26 11:50:26.111050:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:26.111050:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:26.111050:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:26.111050:   - 设备类型: LSGControlCenter
2025-08-26 11:50:26.111050:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:26.111050:   - 数据长度: 8
2025-08-26 11:50:26.111050:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:26.112049:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:26.112049:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:26.112049:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:26.112049:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:26.112049:   - 设备类型: LSGControlCenter
2025-08-26 11:50:26.112049:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:26.112049:   - 数据长度: 8
2025-08-26 11:50:26.112049:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:26.113049:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:26.113049:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:26.113049:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:26.113049:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:26.113049:   - 设备类型: LSGControlCenter
2025-08-26 11:50:26.113049:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:26.114050:   - 数据长度: 8
2025-08-26 11:50:26.114050:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:26.114050:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:26.114050:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:26.114050:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:26.114050:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:26.114050:   - 设备类型: LSGControlCenter
2025-08-26 11:50:26.115049:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:26.115049:   - 数据长度: 8
2025-08-26 11:50:26.115049:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:26.115049:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:26.115049: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:26.115049:   - 发现标签数量: 4
2025-08-26 11:50:26.116054:   - 标签详情:
2025-08-26 11:50:26.116054:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:26.116054:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:26.116054:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:26.117050:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:26.117050: RFID扫描: 发现 4 个标签
2025-08-26 11:50:26.117050: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:26.117050: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:26.118050: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:26.118050:   - UID: E004015305F83C8E
2025-08-26 11:50:26.118050:   - Data: E004015305F83C8E
2025-08-26 11:50:26.118050:   - EventType: 1
2025-08-26 11:50:26.118050:   - Direction: 0
2025-08-26 11:50:26.118050:   - Antenna: 1
2025-08-26 11:50:26.119050:   - TagFrequency: 0
2025-08-26 11:50:26.119050: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:26.119050:   - UID: E004015305F83C8E
2025-08-26 11:50:26.119050:   - Data: E004015305F83C8E
2025-08-26 11:50:26.119050:   - EventType: 1
2025-08-26 11:50:26.119050:   - Direction: 0
2025-08-26 11:50:26.119050:   - Antenna: 1
2025-08-26 11:50:26.120050:   - TagFrequency: 0
2025-08-26 11:50:26.120050: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:26.120050:   - UID: E004015305F83C8E
2025-08-26 11:50:26.120050:   - Data: E004015305F83C8E
2025-08-26 11:50:26.120050:   - EventType: 1
2025-08-26 11:50:26.120050:   - Direction: 0
2025-08-26 11:50:26.120050:   - Antenna: 1
2025-08-26 11:50:26.121049:   - TagFrequency: 0
2025-08-26 11:50:26.121049: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:26.121049:   - UID: E004015305F83C8E
2025-08-26 11:50:26.121049:   - Data: E004015305F83C8E
2025-08-26 11:50:26.121049:   - EventType: 1
2025-08-26 11:50:26.121049:   - Direction: 0
2025-08-26 11:50:26.121049:   - Antenna: 1
2025-08-26 11:50:26.122050:   - TagFrequency: 0
2025-08-26 11:50:26.591042: 🔄 开始RFID轮询检查...
2025-08-26 11:50:26.591042: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:26.591042: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:26.591042: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:26.591042: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:26.611041: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:26.611041:   - 设备句柄: 2395416852128
2025-08-26 11:50:26.611041:   - FetchRecords返回值: 0
2025-08-26 11:50:26.611041:   - 报告数量: 4
2025-08-26 11:50:26.611041:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:26.612041:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:26.612041:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:26.612041:   - 设备类型: LSGControlCenter
2025-08-26 11:50:26.612041:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:26.612041:   - 数据长度: 8
2025-08-26 11:50:26.612041:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:26.613041:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:26.613041:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:26.613041:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:26.613041:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:26.613041:   - 设备类型: LSGControlCenter
2025-08-26 11:50:26.613041:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:26.613041:   - 数据长度: 8
2025-08-26 11:50:26.614041:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:26.614041:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:26.614041:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:26.614041:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:26.614041:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:26.614041:   - 设备类型: LSGControlCenter
2025-08-26 11:50:26.614041:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:26.614041:   - 数据长度: 8
2025-08-26 11:50:26.615041:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:26.615041:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:26.615041:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:26.615041:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:26.615041:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:26.615041:   - 设备类型: LSGControlCenter
2025-08-26 11:50:26.615041:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:26.615041:   - 数据长度: 8
2025-08-26 11:50:26.616041:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:26.616041:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:26.616041: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:26.616041:   - 发现标签数量: 4
2025-08-26 11:50:26.616041:   - 标签详情:
2025-08-26 11:50:26.616041:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:26.616041:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:26.616041:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:26.617041:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:26.617041: RFID扫描: 发现 4 个标签
2025-08-26 11:50:26.617041: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:26.617041: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:26.617041: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:26.617041:   - UID: E004015305F83C8E
2025-08-26 11:50:26.617041:   - Data: E004015305F83C8E
2025-08-26 11:50:26.617041:   - EventType: 1
2025-08-26 11:50:26.618041:   - Direction: 0
2025-08-26 11:50:26.618041:   - Antenna: 1
2025-08-26 11:50:26.618041:   - TagFrequency: 0
2025-08-26 11:50:26.618041: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:26.618041:   - UID: E004015305F83C8E
2025-08-26 11:50:26.618041:   - Data: E004015305F83C8E
2025-08-26 11:50:26.618041:   - EventType: 1
2025-08-26 11:50:26.618041:   - Direction: 0
2025-08-26 11:50:26.619041:   - Antenna: 1
2025-08-26 11:50:26.619041:   - TagFrequency: 0
2025-08-26 11:50:26.619041: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:26.619041:   - UID: E004015305F83C8E
2025-08-26 11:50:26.619041:   - Data: E004015305F83C8E
2025-08-26 11:50:26.619041:   - EventType: 1
2025-08-26 11:50:26.619041:   - Direction: 0
2025-08-26 11:50:26.619041:   - Antenna: 1
2025-08-26 11:50:26.619041:   - TagFrequency: 0
2025-08-26 11:50:26.620041: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:26.620041:   - UID: E004015305F83C8E
2025-08-26 11:50:26.620041:   - Data: E004015305F83C8E
2025-08-26 11:50:26.620041:   - EventType: 1
2025-08-26 11:50:26.620041:   - Direction: 0
2025-08-26 11:50:26.620041:   - Antenna: 1
2025-08-26 11:50:26.620041:   - TagFrequency: 0
2025-08-26 11:50:27.090032: 🔄 开始RFID轮询检查...
2025-08-26 11:50:27.090032: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:27.090032: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:27.090032: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:27.090032: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:27.110033: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:27.110033:   - 设备句柄: 2395416852128
2025-08-26 11:50:27.111036:   - FetchRecords返回值: 0
2025-08-26 11:50:27.111036:   - 报告数量: 4
2025-08-26 11:50:27.111036:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:27.111036:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:27.111036:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:27.112032:   - 设备类型: LSGControlCenter
2025-08-26 11:50:27.112032:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:27.112032:   - 数据长度: 8
2025-08-26 11:50:27.112032:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:27.112032:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:27.112032:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:27.112032:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:27.112032:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:27.113032:   - 设备类型: LSGControlCenter
2025-08-26 11:50:27.113032:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:27.113032:   - 数据长度: 8
2025-08-26 11:50:27.113032:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:27.113032:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:27.113032:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:27.113032:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:27.114032:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:27.114032:   - 设备类型: LSGControlCenter
2025-08-26 11:50:27.114032:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:27.114032:   - 数据长度: 8
2025-08-26 11:50:27.114032:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:27.114032:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:27.114032:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:27.114032:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:27.115032:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:27.115032:   - 设备类型: LSGControlCenter
2025-08-26 11:50:27.115032:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:27.115032:   - 数据长度: 8
2025-08-26 11:50:27.115032:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:27.115032:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:27.115032: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:27.116032:   - 发现标签数量: 4
2025-08-26 11:50:27.116032:   - 标签详情:
2025-08-26 11:50:27.116032:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:27.116032:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:27.116032:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:27.116032:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:27.116032: RFID扫描: 发现 4 个标签
2025-08-26 11:50:27.117032: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:27.117032: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:27.117032: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:27.117032:   - UID: E004015305F83C8E
2025-08-26 11:50:27.117032:   - Data: E004015305F83C8E
2025-08-26 11:50:27.117032:   - EventType: 1
2025-08-26 11:50:27.117032:   - Direction: 0
2025-08-26 11:50:27.118032:   - Antenna: 1
2025-08-26 11:50:27.118032:   - TagFrequency: 0
2025-08-26 11:50:27.118032: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:27.118032:   - UID: E004015305F83C8E
2025-08-26 11:50:27.118032:   - Data: E004015305F83C8E
2025-08-26 11:50:27.119032:   - EventType: 1
2025-08-26 11:50:27.119032:   - Direction: 0
2025-08-26 11:50:27.119032:   - Antenna: 1
2025-08-26 11:50:27.119032:   - TagFrequency: 0
2025-08-26 11:50:27.120034: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:27.120034:   - UID: E004015305F83C8E
2025-08-26 11:50:27.120034:   - Data: E004015305F83C8E
2025-08-26 11:50:27.121033:   - EventType: 1
2025-08-26 11:50:27.121033:   - Direction: 0
2025-08-26 11:50:27.121033:   - Antenna: 1
2025-08-26 11:50:27.121033:   - TagFrequency: 0
2025-08-26 11:50:27.121033: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:27.122034:   - UID: E004015305F83C8E
2025-08-26 11:50:27.122034:   - Data: E004015305F83C8E
2025-08-26 11:50:27.122034:   - EventType: 1
2025-08-26 11:50:27.122034:   - Direction: 0
2025-08-26 11:50:27.123033:   - Antenna: 1
2025-08-26 11:50:27.123033:   - TagFrequency: 0
2025-08-26 11:50:27.591030: 🔄 开始RFID轮询检查...
2025-08-26 11:50:27.591030: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:27.592025: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:27.592025: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:27.592025: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:27.611023: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:27.611023:   - 设备句柄: 2395416852128
2025-08-26 11:50:27.612024:   - FetchRecords返回值: 0
2025-08-26 11:50:27.612024:   - 报告数量: 4
2025-08-26 11:50:27.612024:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:27.612024:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:27.612024:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:27.612024:   - 设备类型: LSGControlCenter
2025-08-26 11:50:27.613023:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:27.613023:   - 数据长度: 8
2025-08-26 11:50:27.613023:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:27.613023:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:27.613023:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:27.613023:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:27.613023:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:27.614023:   - 设备类型: LSGControlCenter
2025-08-26 11:50:27.614023:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:27.614023:   - 数据长度: 8
2025-08-26 11:50:27.614023:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:27.614023:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:27.614023:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:27.614023:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:27.615023:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:27.615023:   - 设备类型: LSGControlCenter
2025-08-26 11:50:27.615023:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:27.616023:   - 数据长度: 8
2025-08-26 11:50:27.616023:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:27.616023:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:27.616023:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:27.616023:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:27.616023:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:27.616023:   - 设备类型: LSGControlCenter
2025-08-26 11:50:27.617023:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:27.617023:   - 数据长度: 8
2025-08-26 11:50:27.617023:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:27.617023:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:27.617023: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:27.617023:   - 发现标签数量: 4
2025-08-26 11:50:27.617023:   - 标签详情:
2025-08-26 11:50:27.617023:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:27.618023:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:27.618023:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:27.618023:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:27.618023: RFID扫描: 发现 4 个标签
2025-08-26 11:50:27.618023: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:27.618023: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:27.618023: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:27.618023:   - UID: E004015305F83C8E
2025-08-26 11:50:27.619023:   - Data: E004015305F83C8E
2025-08-26 11:50:27.619023:   - EventType: 1
2025-08-26 11:50:27.619023:   - Direction: 0
2025-08-26 11:50:27.619023:   - Antenna: 1
2025-08-26 11:50:27.619023:   - TagFrequency: 0
2025-08-26 11:50:27.619023: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:27.619023:   - UID: E004015305F83C8E
2025-08-26 11:50:27.619023:   - Data: E004015305F83C8E
2025-08-26 11:50:27.620023:   - EventType: 1
2025-08-26 11:50:27.620023:   - Direction: 0
2025-08-26 11:50:27.620023:   - Antenna: 1
2025-08-26 11:50:27.620023:   - TagFrequency: 0
2025-08-26 11:50:27.620023: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:27.620023:   - UID: E004015305F83C8E
2025-08-26 11:50:27.620023:   - Data: E004015305F83C8E
2025-08-26 11:50:27.620023:   - EventType: 1
2025-08-26 11:50:27.621023:   - Direction: 0
2025-08-26 11:50:27.621023:   - Antenna: 1
2025-08-26 11:50:27.621023:   - TagFrequency: 0
2025-08-26 11:50:27.621023: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:27.621023:   - UID: E004015305F83C8E
2025-08-26 11:50:27.621023:   - Data: E004015305F83C8E
2025-08-26 11:50:27.621023:   - EventType: 1
2025-08-26 11:50:27.621023:   - Direction: 0
2025-08-26 11:50:27.621023:   - Antenna: 1
2025-08-26 11:50:27.622023:   - TagFrequency: 0
2025-08-26 11:50:28.091015: 🔄 开始RFID轮询检查...
2025-08-26 11:50:28.091015: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:28.091015: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:28.091015: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:28.092015: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:28.111015: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:28.111015:   - 设备句柄: 2395416852128
2025-08-26 11:50:28.111015:   - FetchRecords返回值: 0
2025-08-26 11:50:28.111015:   - 报告数量: 4
2025-08-26 11:50:28.111015:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:28.112020:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:28.112020:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:28.112020:   - 设备类型: LSGControlCenter
2025-08-26 11:50:28.112020:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:28.112020:   - 数据长度: 8
2025-08-26 11:50:28.112020:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:28.112020:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:28.113015:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:28.113015:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:28.113015:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:28.113015:   - 设备类型: LSGControlCenter
2025-08-26 11:50:28.113015:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:28.113015:   - 数据长度: 8
2025-08-26 11:50:28.113015:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:28.113015:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:28.114015:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:28.114015:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:28.114015:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:28.114015:   - 设备类型: LSGControlCenter
2025-08-26 11:50:28.114015:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:28.114015:   - 数据长度: 8
2025-08-26 11:50:28.114015:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:28.115015:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:28.115015:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:28.115015:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:28.115015:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:28.115015:   - 设备类型: LSGControlCenter
2025-08-26 11:50:28.115015:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:28.115015:   - 数据长度: 8
2025-08-26 11:50:28.116015:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:28.116015:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:28.116015: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:28.116015:   - 发现标签数量: 4
2025-08-26 11:50:28.116015:   - 标签详情:
2025-08-26 11:50:28.116015:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:28.117014:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:28.117014:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:28.117014:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:28.117014: RFID扫描: 发现 4 个标签
2025-08-26 11:50:28.117014: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:28.117014: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:28.117014: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:28.117014:   - UID: E004015305F83C8E
2025-08-26 11:50:28.118014:   - Data: E004015305F83C8E
2025-08-26 11:50:28.118014:   - EventType: 1
2025-08-26 11:50:28.118014:   - Direction: 0
2025-08-26 11:50:28.118014:   - Antenna: 1
2025-08-26 11:50:28.118014:   - TagFrequency: 0
2025-08-26 11:50:28.118014: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:28.118014:   - UID: E004015305F83C8E
2025-08-26 11:50:28.118014:   - Data: E004015305F83C8E
2025-08-26 11:50:28.119014:   - EventType: 1
2025-08-26 11:50:28.119014:   - Direction: 0
2025-08-26 11:50:28.119014:   - Antenna: 1
2025-08-26 11:50:28.119014:   - TagFrequency: 0
2025-08-26 11:50:28.119014: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:28.119014:   - UID: E004015305F83C8E
2025-08-26 11:50:28.119014:   - Data: E004015305F83C8E
2025-08-26 11:50:28.119014:   - EventType: 1
2025-08-26 11:50:28.120014:   - Direction: 0
2025-08-26 11:50:28.120014:   - Antenna: 1
2025-08-26 11:50:28.120014:   - TagFrequency: 0
2025-08-26 11:50:28.120014: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:28.120014:   - UID: E004015305F83C8E
2025-08-26 11:50:28.120014:   - Data: E004015305F83C8E
2025-08-26 11:50:28.120014:   - EventType: 1
2025-08-26 11:50:28.121014:   - Direction: 0
2025-08-26 11:50:28.121014:   - Antenna: 1
2025-08-26 11:50:28.121014:   - TagFrequency: 0
2025-08-26 11:50:28.591006: 🔄 开始RFID轮询检查...
2025-08-26 11:50:28.591006: 📊 轮询状态: 扫描中=true, tagList=1个标签, 已处理=1个
2025-08-26 11:50:28.591006: 🏷️ 检查标签: barcode=null, uid=E004015305F83C8E
2025-08-26 11:50:28.591006: 🔄 标签已处理(UID): E004015305F83C8E
2025-08-26 11:50:28.592007: 🔄 轮询完成: 无新标签，已处理1个标签
2025-08-26 11:50:28.611006: 🔍 LSGate硬件扫描详情:
2025-08-26 11:50:28.612007:   - 设备句柄: 2395416852128
2025-08-26 11:50:28.613007:   - FetchRecords返回值: 0
2025-08-26 11:50:28.613007:   - 报告数量: 4
2025-08-26 11:50:28.613007:   - 报告1 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:28.613007:   - 原始数据: 01 00 19 08 1A 0B 33 0F 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:28.613007:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:28.614006:   - 设备类型: LSGControlCenter
2025-08-26 11:50:28.614006:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:28.614006:   - 数据长度: 8
2025-08-26 11:50:28.614006:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:28.614006:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:28.615007:   - 报告2 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:28.615007:   - 原始数据: 01 00 19 08 1A 0B 33 11 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:28.615007:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:28.615007:   - 设备类型: LSGControlCenter
2025-08-26 11:50:28.615007:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:28.615007:   - 数据长度: 8
2025-08-26 11:50:28.616006:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:28.616006:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:28.616006:   - 报告3 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:28.616006:   - 原始数据: 01 00 19 08 1A 0B 33 14 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:28.617007:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:28.617007:   - 设备类型: LSGControlCenter
2025-08-26 11:50:28.617007:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:28.617007:   - 数据长度: 8
2025-08-26 11:50:28.618006:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:28.618006:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:28.618006:   - 报告4 解析结果: parseRet=0, 数据长度=19
2025-08-26 11:50:28.618006:   - 原始数据: 01 00 19 08 1A 0B 33 16 09 E0 04 01 53 05 F8 3C 8E 00 00
2025-08-26 11:50:28.618006:   - 🎯 使用LSGControlCenter固定位置解析，UID位置: 9-16
2025-08-26 11:50:28.618006:   - 设备类型: LSGControlCenter
2025-08-26 11:50:28.619006:   - 事件类型: 1, 方向: 0
2025-08-26 11:50:28.619006:   - 数据长度: 8
2025-08-26 11:50:28.619006:   - 标签数据: E0 04 01 53 05 F8 3C 8E
2025-08-26 11:50:28.619006:   - 提取的UID: E004015305F83C8E
2025-08-26 11:50:28.619006: 📊 LSGate扫描结果汇总:
2025-08-26 11:50:28.619006:   - 发现标签数量: 4
2025-08-26 11:50:28.619006:   - 标签详情:
2025-08-26 11:50:28.620006:     [0] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:28.620006:     [1] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:28.620006:     [2] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:28.620006:     [3] UID: E004015305F83C8E, 事件: 1, 方向: 0
2025-08-26 11:50:28.621006: RFID扫描: 发现 4 个标签
2025-08-26 11:50:28.621006: 🔍 LSGate ReaderManager收到扫描结果: 4 个UID
2025-08-26 11:50:28.621006: 🎉 LSGate检测到RFID标签！
2025-08-26 11:50:28.621006: 🏷️ LSGate UID[0]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 15], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:28.622006:   - UID: E004015305F83C8E
2025-08-26 11:50:28.622006:   - Data: E004015305F83C8E
2025-08-26 11:50:28.622006:   - EventType: 1
2025-08-26 11:50:28.622006:   - Direction: 0
2025-08-26 11:50:28.623008:   - Antenna: 1
2025-08-26 11:50:28.623008:   - TagFrequency: 0
2025-08-26 11:50:28.623008: 🏷️ LSGate UID[1]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 17], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:28.623008:   - UID: E004015305F83C8E
2025-08-26 11:50:28.623008:   - Data: E004015305F83C8E
2025-08-26 11:50:28.624006:   - EventType: 1
2025-08-26 11:50:28.624006:   - Direction: 0
2025-08-26 11:50:28.624006:   - Antenna: 1
2025-08-26 11:50:28.624006:   - TagFrequency: 0
2025-08-26 11:50:28.624006: 🏷️ LSGate UID[2]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 20], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:28.624006:   - UID: E004015305F83C8E
2025-08-26 11:50:28.624006:   - Data: E004015305F83C8E
2025-08-26 11:50:28.625006:   - EventType: 1
2025-08-26 11:50:28.625006:   - Direction: 0
2025-08-26 11:50:28.625006:   - Antenna: 1
2025-08-26 11:50:28.625006:   - TagFrequency: 0
2025-08-26 11:50:28.625006: 🏷️ LSGate UID[3]: {uid: E004015305F83C8E, data: E004015305F83C8E, eventType: 1, direction: 0, time: [25, 8, 26, 11, 51, 22], inAnt: 1, tagFrequency: 0, decodedData: {"oidList":[{"oid":0,"compressMode":"110","data":"S","originHexStr":"E004015300000000","originData":["53"],"isKeepInTag":true}]}}
2025-08-26 11:50:28.625006:   - UID: E004015305F83C8E
2025-08-26 11:50:28.625006:   - Data: E004015305F83C8E
2025-08-26 11:50:28.626006:   - EventType: 1
2025-08-26 11:50:28.626006:   - Direction: 0
2025-08-26 11:50:28.626006:   - Antenna: 1
2025-08-26 11:50:28.626006:   - TagFrequency: 0
